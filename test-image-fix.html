<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Fix - Bible Picture Story Display</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .console-output {
            background-color: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Image Fix - Bible Picture Story Display</h1>
    
    <div class="test-section">
        <h2>🧪 Automated Image Loading Test</h2>
        <p>This test will automatically create a test image and send it to a projector window to verify the fix.</p>
        <button onclick="runAutomatedTest()">Run Automated Test</button>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📝 Console Output</h2>
        <div id="consoleOutput" class="console-output">Test console output will appear here...</div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <script>
        let projectorWindow = null;

        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌ ERROR' : type === 'warn' ? '⚠️ WARN' : '📝 LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function addResult(message, type) {
            const container = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function createTestImage() {
            // Create a simple test image using canvas
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 300;
            const ctx = canvas.getContext('2d');
            
            // Create a gradient background
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(1, '#2196F3');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);
            
            // Add some text
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('TEST IMAGE', 200, 120);
            ctx.font = '16px Arial';
            ctx.fillText('Bible Picture Story Display', 200, 150);
            ctx.fillText('Image Loading Test', 200, 180);
            
            // Add timestamp
            const timestamp = new Date().toLocaleString();
            ctx.font = '12px Arial';
            ctx.fillText(`Generated: ${timestamp}`, 200, 220);
            
            // Convert to data URL
            return canvas.toDataURL('image/png');
        }

        function runAutomatedTest() {
            const container = document.getElementById('testResults');
            container.innerHTML = '';
            
            console.log('Starting automated image loading test...');
            addResult('Starting automated test...', 'info');
            
            try {
                // Step 1: Create test image
                console.log('Creating test image...');
                const testImageDataURL = createTestImage();
                console.log('Test image created, length:', testImageDataURL.length);
                addResult('✓ Test image created successfully', 'pass');
                
                // Step 2: Create test slide data
                const testSlide = {
                    id: Date.now(),
                    name: 'Test Image',
                    src: testImageDataURL,
                    text: 'This is a test image to verify the fix',
                    textStyle: {
                        fontSize: 24,
                        color: '#ffffff',
                        bold: true,
                        italic: false,
                        underline: false,
                        alignment: 'center'
                    }
                };
                
                console.log('Test slide data created:', testSlide);
                addResult('✓ Test slide data prepared', 'pass');
                
                // Step 3: Open projector window
                console.log('Opening projector window...');
                projectorWindow = window.open('projector.html', 'testProjector', 'width=800,height=600');
                
                if (!projectorWindow) {
                    throw new Error('Failed to open projector window (popup blocked?)');
                }
                
                addResult('✓ Projector window opened', 'pass');
                
                // Step 4: Wait for projector to load, then send test data
                setTimeout(() => {
                    console.log('Sending INIT message to projector...');
                    
                    // Send INIT message first
                    projectorWindow.postMessage({
                        type: 'INIT',
                        data: {
                            stories: [testSlide],
                            currentIndex: 0,
                            animationSettings: {
                                speed: 1.0,
                                duration: 500
                            }
                        }
                    }, '*');
                    
                    addResult('✓ INIT message sent', 'pass');
                    
                    // Wait a bit, then send slide change
                    setTimeout(() => {
                        console.log('Sending SLIDE_CHANGE message to projector...');
                        
                        projectorWindow.postMessage({
                            type: 'SLIDE_CHANGE',
                            data: {
                                slide: testSlide,
                                index: 0
                            }
                        }, '*');
                        
                        addResult('✓ SLIDE_CHANGE message sent', 'pass');
                        addResult('Check the projector window - the test image should be displayed', 'info');
                        
                        // Listen for any error messages
                        setTimeout(() => {
                            addResult('Test completed. If you see the test image in the projector window, the fix is working!', 'info');
                        }, 1000);
                        
                    }, 1000);
                    
                }, 2000);
                
            } catch (error) {
                console.error('Test failed:', error);
                addResult(`❌ Test failed: ${error.message}`, 'fail');
            }
        }

        function clearConsole() {
            document.getElementById('consoleOutput').textContent = 'Console cleared...\n';
        }

        // Listen for messages from projector window
        window.addEventListener('message', function(event) {
            console.log('Received message from projector:', event.data);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (projectorWindow && !projectorWindow.closed) {
                projectorWindow.close();
            }
        });

        console.log('Test page loaded and ready');
    </script>
</body>
</html>
