<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bible Picture Story Display - Control Panel</title>
    <link rel="stylesheet" href="css/control-panel.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <h1><i class="fas fa-book-open"></i> Bible Picture Story Display</h1>
                <span class="subtitle">Control Panel</span>
            </div>
            <div class="header-right">
                <button id="openProjectorBtn" class="btn btn-primary">
                    <i class="fas fa-external-link-alt"></i> Open Projector Display
                </button>
                <button id="exportStoryBtn" class="btn btn-secondary">
                    <i class="fas fa-download"></i> Export
                </button>
                <button id="importStoryBtn" class="btn btn-secondary">
                    <i class="fas fa-upload"></i> Import
                </button>
                <input type="file" id="importFileInput" accept=".json" style="display: none;">
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Panel - Story Management -->
            <div class="left-panel">
                <div class="panel-section">
                    <h3><i class="fas fa-images"></i> Story Images</h3>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-placeholder">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag & drop images here or click to browse</p>
                            <input type="file" id="imageUpload" multiple accept="image/*" style="display: none;">
                        </div>
                    </div>
                    <div class="image-list" id="imageList">
                        <!-- Images will be dynamically added here -->
                    </div>
                </div>

                <div class="panel-section">
                    <h3><i class="fas fa-play-circle"></i> Navigation Controls</h3>
                    <div class="nav-controls">
                        <button id="prevSlideBtn" class="btn btn-nav" disabled>
                            <i class="fas fa-step-backward"></i> Previous
                        </button>
                        <span class="slide-counter" id="slideCounter">0 / 0</span>
                        <button id="nextSlideBtn" class="btn btn-nav" disabled>
                            <i class="fas fa-step-forward"></i> Next
                        </button>
                    </div>
                    <div class="jump-controls">
                        <label for="jumpToSlide">Jump to slide:</label>
                        <input type="number" id="jumpToSlide" min="1" placeholder="1">
                        <button id="jumpBtn" class="btn btn-small">Go</button>
                    </div>
                </div>
            </div>

            <!-- Center Panel - Preview -->
            <div class="center-panel">
                <div class="preview-container">
                    <div class="preview-header">
                        <h3><i class="fas fa-eye"></i> Preview</h3>
                        <div class="preview-controls">
                            <button id="fullscreenPreviewBtn" class="btn btn-small">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="preview-area" id="previewArea">
                        <div class="preview-placeholder">
                            <i class="fas fa-image"></i>
                            <p>No image selected</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Text & Animation Controls -->
            <div class="right-panel">
                <div class="panel-section">
                    <h3><i class="fas fa-text-height"></i> Text Overlay</h3>
                    <div class="text-controls">
                        <textarea id="textOverlay" placeholder="Enter text overlay for current slide..."></textarea>
                        <div class="text-formatting">
                            <div class="format-row">
                                <label>Font Size:</label>
                                <input type="range" id="fontSize" min="12" max="72" value="24">
                                <span id="fontSizeValue">24px</span>
                            </div>
                            <div class="format-row">
                                <label>Color:</label>
                                <input type="color" id="textColor" value="#ffffff">
                            </div>
                            <div class="format-row">
                                <button id="boldBtn" class="format-btn"><i class="fas fa-bold"></i></button>
                                <button id="italicBtn" class="format-btn"><i class="fas fa-italic"></i></button>
                                <button id="underlineBtn" class="format-btn"><i class="fas fa-underline"></i></button>
                            </div>
                            <div class="format-row">
                                <label>Alignment:</label>
                                <button id="alignLeftBtn" class="format-btn"><i class="fas fa-align-left"></i></button>
                                <button id="alignCenterBtn" class="format-btn active"><i class="fas fa-align-center"></i></button>
                                <button id="alignRightBtn" class="format-btn"><i class="fas fa-align-right"></i></button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-section">
                    <h3><i class="fas fa-magic"></i> Animation Controls</h3>
                    <div class="animation-controls">
                        <div class="animation-group">
                            <label>Zoom:</label>
                            <div class="control-buttons">
                                <button id="zoomInBtn" class="btn btn-animation">
                                    <i class="fas fa-search-plus"></i> Zoom In
                                </button>
                                <button id="zoomOutBtn" class="btn btn-animation">
                                    <i class="fas fa-search-minus"></i> Zoom Out
                                </button>
                                <button id="resetZoomBtn" class="btn btn-animation">
                                    <i class="fas fa-compress"></i> Reset
                                </button>
                            </div>
                        </div>
                        <div class="animation-group">
                            <label>Pan:</label>
                            <div class="control-buttons pan-controls">
                                <button id="panUpBtn" class="btn btn-animation">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                                <div class="pan-middle">
                                    <button id="panLeftBtn" class="btn btn-animation">
                                        <i class="fas fa-arrow-left"></i>
                                    </button>
                                    <button id="panCenterBtn" class="btn btn-animation">
                                        <i class="fas fa-crosshairs"></i>
                                    </button>
                                    <button id="panRightBtn" class="btn btn-animation">
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                                <button id="panDownBtn" class="btn btn-animation">
                                    <i class="fas fa-arrow-down"></i>
                                </button>
                            </div>
                        </div>
                        <div class="animation-group">
                            <label>Speed:</label>
                            <input type="range" id="animationSpeed" min="0.5" max="3" step="0.1" value="1">
                            <span id="speedValue">1.0x</span>
                        </div>
                        <div class="animation-group">
                            <label>Duration:</label>
                            <input type="range" id="animationDuration" min="500" max="5000" step="100" value="1000">
                            <span id="durationValue">1.0s</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <footer class="status-bar">
            <div class="status-left">
                <span id="connectionStatus">
                    <i class="fas fa-circle status-disconnected"></i> Projector Disconnected
                </span>
            </div>
            <div class="status-right">
                <span id="autoSaveStatus">Auto-save: On</span>
                <span id="lastSaved">Last saved: Never</span>
            </div>
        </footer>
    </div>

    <!-- Keyboard Shortcuts Help Modal -->
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Keyboard Shortcuts</h2>
            <div class="shortcuts-list">
                <div class="shortcut-item">
                    <kbd>←</kbd> <span>Previous slide</span>
                </div>
                <div class="shortcut-item">
                    <kbd>→</kbd> <span>Next slide</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Space</kbd> <span>Next slide</span>
                </div>
                <div class="shortcut-item">
                    <kbd>F</kbd> <span>Toggle fullscreen</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl + S</kbd> <span>Save story</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl + O</kbd> <span>Open projector</span>
                </div>
                <div class="shortcut-item">
                    <kbd>?</kbd> <span>Show this help</span>
                </div>
            </div>
        </div>
    </div>

    <script src="js/control-panel.js"></script>
</body>
</html>
