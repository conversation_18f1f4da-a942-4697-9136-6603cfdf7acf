<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Loading Test - Bible Picture Story Display</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #007bff;
            padding: 40px;
            text-align: center;
            margin: 15px 0;
            border-radius: 8px;
            background-color: #f8f9fa;
            cursor: pointer;
        }
        .upload-area:hover, .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #0056b3;
        }
        .image-preview {
            max-width: 300px;
            max-height: 200px;
            border: 1px solid #ddd;
            margin: 10px;
            display: none;
        }
        .console-output {
            background-color: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🖼️ Image Loading Test - Bible Picture Story Display</h1>
    
    <div class="test-section">
        <h2>📤 Image Upload Test</h2>
        <div class="upload-area" id="uploadArea">
            <p><strong>Upload an image to test</strong></p>
            <p>Drag & drop an image here or click to select</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>
        <div id="uploadResults"></div>
        <img id="previewImage" class="image-preview" alt="Preview">
    </div>

    <div class="test-section">
        <h2>🔗 Window Communication Test</h2>
        <button onclick="testWindowCommunication()">Test Projector Communication</button>
        <div id="communicationResults"></div>
    </div>

    <div class="test-section">
        <h2>📊 Data URL Analysis</h2>
        <div id="dataAnalysis"></div>
    </div>

    <div class="test-section">
        <h2>📝 Console Output</h2>
        <div id="consoleOutput" class="console-output">Console messages will appear here...</div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <script>
        let currentImageData = null;
        let testWindow = null;

        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌ ERROR' : type === 'warn' ? '⚠️ WARN' : '📝 LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function addResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const previewImage = document.getElementById('previewImage');

        uploadArea.addEventListener('click', () => fileInput.click());

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                processFile(files[0]);
            }
        });

        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                processFile(e.target.files[0]);
            }
        });

        function processFile(file) {
            console.log('Processing file:', file.name, file.type, file.size);
            
            const container = document.getElementById('uploadResults');
            container.innerHTML = '';
            
            addResult('uploadResults', `File selected: ${file.name}`, 'info');
            addResult('uploadResults', `File type: ${file.type}`, 'info');
            addResult('uploadResults', `File size: ${(file.size / 1024).toFixed(1)} KB`, 'info');

            if (!file.type.startsWith('image/')) {
                addResult('uploadResults', 'Error: Not an image file', 'fail');
                return;
            }

            const reader = new FileReader();
            
            reader.onload = function(e) {
                const result = e.target.result;
                console.log('File read complete');
                console.log('Data URL length:', result.length);
                console.log('Data URL preview:', result.substring(0, 100) + '...');
                
                currentImageData = {
                    name: file.name,
                    src: result,
                    type: file.type,
                    size: file.size
                };
                
                addResult('uploadResults', 'File read successfully', 'pass');
                addResult('uploadResults', `Data URL length: ${result.length}`, 'info');
                
                // Test image loading
                testImageLoading(result);
                
                // Analyze data URL
                analyzeDataURL(result);
            };
            
            reader.onerror = function(e) {
                console.error('Error reading file:', e);
                addResult('uploadResults', 'Error reading file', 'fail');
            };
            
            reader.readAsDataURL(file);
        }

        function testImageLoading(dataURL) {
            console.log('Testing image loading...');
            
            previewImage.onload = function() {
                console.log('Image loaded successfully in preview');
                addResult('uploadResults', 'Image preview loaded successfully', 'pass');
                previewImage.style.display = 'block';
            };
            
            previewImage.onerror = function() {
                console.error('Image failed to load in preview');
                addResult('uploadResults', 'Image preview failed to load', 'fail');
            };
            
            previewImage.src = dataURL;
        }

        function analyzeDataURL(dataURL) {
            const container = document.getElementById('dataAnalysis');
            container.innerHTML = '';
            
            const commaIndex = dataURL.indexOf(',');
            if (commaIndex === -1) {
                addResult('dataAnalysis', 'Invalid data URL - no comma found', 'fail');
                return;
            }
            
            const header = dataURL.substring(0, commaIndex);
            const data = dataURL.substring(commaIndex + 1);
            
            addResult('dataAnalysis', `Header: ${header}`, 'info');
            addResult('dataAnalysis', `Data length: ${data.length}`, 'info');
            addResult('dataAnalysis', `Total length: ${dataURL.length}`, 'info');
            
            // Check if it's base64
            const isBase64 = header.includes('base64');
            addResult('dataAnalysis', `Base64 encoded: ${isBase64}`, isBase64 ? 'pass' : 'fail');
            
            // Check if it's an image
            const isImage = header.includes('image/');
            addResult('dataAnalysis', `Image type: ${isImage}`, isImage ? 'pass' : 'fail');
            
            // Try to decode a small portion to verify
            if (isBase64) {
                try {
                    const sample = data.substring(0, 100);
                    atob(sample);
                    addResult('dataAnalysis', 'Base64 data appears valid', 'pass');
                } catch (e) {
                    addResult('dataAnalysis', 'Base64 data appears corrupted', 'fail');
                }
            }
        }

        function testWindowCommunication() {
            const container = document.getElementById('communicationResults');
            container.innerHTML = '';
            
            if (!currentImageData) {
                addResult('communicationResults', 'Please upload an image first', 'fail');
                return;
            }
            
            console.log('Testing window communication...');
            
            try {
                // Open a test projector window
                testWindow = window.open('projector.html', 'testProjector', 'width=800,height=600');
                
                if (!testWindow) {
                    addResult('communicationResults', 'Failed to open projector window (popup blocked?)', 'fail');
                    return;
                }
                
                addResult('communicationResults', 'Projector window opened', 'pass');
                
                // Wait for window to load, then send test data
                setTimeout(() => {
                    console.log('Sending test data to projector...');
                    
                    const testData = {
                        type: 'SLIDE_CHANGE',
                        data: {
                            slide: currentImageData,
                            index: 0
                        }
                    };
                    
                    console.log('Test data:', testData);
                    testWindow.postMessage(testData, '*');
                    
                    addResult('communicationResults', 'Test data sent to projector', 'pass');
                    addResult('communicationResults', 'Check projector window for results', 'info');
                    
                }, 2000);
                
            } catch (error) {
                console.error('Error in window communication test:', error);
                addResult('communicationResults', `Error: ${error.message}`, 'fail');
            }
        }

        function clearConsole() {
            document.getElementById('consoleOutput').textContent = 'Console cleared...\n';
        }

        // Listen for messages from projector window
        window.addEventListener('message', function(event) {
            console.log('Received message from projector:', event.data);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (testWindow && !testWindow.closed) {
                testWindow.close();
            }
        });

        console.log('Image test page loaded');
    </script>
</body>
</html>
