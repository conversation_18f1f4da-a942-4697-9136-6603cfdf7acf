<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bible Picture Story Display - Projector</title>
    <link rel="stylesheet" href="css/projector.css">
</head>
<body>
    <div class="projector-container">
        <!-- Main Display Area -->
        <div class="display-area" id="displayArea">
            <div class="image-container" id="imageContainer">
                <img id="currentImage" src="" alt="" style="display: none;">
                <div class="placeholder" id="placeholder">
                    <div class="placeholder-content">
                        <i class="cross-icon">✝</i>
                        <h1>Bible Picture Story Display</h1>
                        <p>Waiting for story to begin...</p>
                    </div>
                </div>
            </div>
            
            <!-- Text Overlay -->
            <div class="text-overlay" id="textOverlay">
                <!-- Text content will be dynamically added here -->
            </div>
        </div>

        <!-- Loading Indicator -->
        <div class="loading-indicator" id="loadingIndicator" style="display: none;">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>

        <!-- Connection Status -->
        <div class="connection-status" id="connectionStatus">
            <div class="status-indicator">
                <i class="status-icon">●</i>
                <span class="status-text">Connecting to Control Panel...</span>
            </div>
        </div>

        <!-- Slide Information (for debugging, can be hidden in production) -->
        <div class="slide-info" id="slideInfo" style="display: none;">
            <span id="slideNumber">Slide 0 of 0</span>
        </div>
    </div>

    <!-- Transition Overlay for smooth transitions -->
    <div class="transition-overlay" id="transitionOverlay"></div>

    <script src="js/projector.js"></script>
</body>
</html>
