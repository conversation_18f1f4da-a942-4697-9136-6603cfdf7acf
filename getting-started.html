<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bible Picture Story Display - Getting Started</title>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #5cb85c;
            --bg-color: #f8f9fa;
            --text-color: #333;
            --border-color: #dee2e6;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 10px;
            box-shadow: var(--shadow);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .quick-start {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .card h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .card p {
            margin-bottom: 1.5rem;
            color: #666;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn:hover {
            background-color: #357abd;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-secondary:hover {
            background-color: #449d44;
            box-shadow: 0 4px 12px rgba(92, 184, 92, 0.3);
        }

        .features {
            margin-bottom: 3rem;
        }

        .features h2 {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary-color);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .feature {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
            box-shadow: var(--shadow);
        }

        .feature h4 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .feature p {
            color: #666;
            font-size: 0.9rem;
        }

        .instructions {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }

        .instructions h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
        }

        .instructions ol {
            margin-left: 1.5rem;
        }

        .instructions li {
            margin-bottom: 0.5rem;
        }

        .footer {
            text-align: center;
            padding: 2rem;
            color: #666;
            border-top: 1px solid var(--border-color);
        }

        .icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            display: block;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .quick-start {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📖 Bible Picture Story Display</h1>
            <p>Create and present engaging Bible stories with professional dual-window control</p>
        </div>

        <div class="quick-start">
            <div class="card">
                <span class="icon">🚀</span>
                <h3>Start Creating</h3>
                <p>Launch the control panel to begin creating your Bible picture story presentation.</p>
                <a href="index.html" class="btn">Open Control Panel</a>
            </div>

            <div class="card">
                <span class="icon">📋</span>
                <h3>Try Sample Story</h3>
                <p>Import a sample "David and Goliath" story to see how the application works.</p>
                <button onclick="downloadSample()" class="btn btn-secondary">Download Sample</button>
            </div>

            <div class="card">
                <span class="icon">📚</span>
                <h3>Read Documentation</h3>
                <p>Learn about all features and get detailed usage instructions.</p>
                <a href="#instructions" class="btn">View Instructions</a>
            </div>
        </div>

        <div class="features">
            <h2>Key Features</h2>
            <div class="feature-grid">
                <div class="feature">
                    <h4>🎮 Dual Window Control</h4>
                    <p>Separate control panel and projector display for professional presentations</p>
                </div>
                <div class="feature">
                    <h4>🖼️ Image Management</h4>
                    <p>Drag-and-drop upload, reordering, and organization of story images</p>
                </div>
                <div class="feature">
                    <h4>✏️ Text Overlay Editor</h4>
                    <p>Rich text formatting with fonts, colors, and alignment options</p>
                </div>
                <div class="feature">
                    <h4>🎬 Animation Controls</h4>
                    <p>Real-time zoom and pan effects with adjustable speed and duration</p>
                </div>
                <div class="feature">
                    <h4>💾 Export/Import</h4>
                    <p>Save and share story configurations in portable JSON format</p>
                </div>
                <div class="feature">
                    <h4>⌨️ Keyboard Shortcuts</h4>
                    <p>Quick navigation and control during live presentations</p>
                </div>
            </div>
        </div>

        <div class="instructions" id="instructions">
            <h2>Quick Start Instructions</h2>
            <ol>
                <li><strong>Open the Control Panel:</strong> Click "Open Control Panel" above to launch the main interface</li>
                <li><strong>Upload Images:</strong> Drag and drop your Bible story images into the upload area</li>
                <li><strong>Arrange Slides:</strong> Reorder images by dragging them up or down in the list</li>
                <li><strong>Add Text:</strong> Select each slide and add text overlays with formatting</li>
                <li><strong>Open Projector:</strong> Click "Open Projector Display" to launch the presentation window</li>
                <li><strong>Position Windows:</strong> Move the projector window to your extended display/projector</li>
                <li><strong>Present:</strong> Use navigation controls and animations during your presentation</li>
                <li><strong>Save Work:</strong> Export your story or rely on auto-save to preserve your work</li>
            </ol>
        </div>

        <div class="instructions">
            <h2>System Requirements</h2>
            <ul style="margin-left: 1.5rem;">
                <li><strong>Browser:</strong> Chrome 60+, Firefox 55+, Safari 12+, or Edge 79+</li>
                <li><strong>JavaScript:</strong> Must be enabled</li>
                <li><strong>Pop-ups:</strong> Allow pop-ups for projector window</li>
                <li><strong>Screen:</strong> Extended display recommended for dual-window setup</li>
                <li><strong>Images:</strong> JPG, PNG, or GIF format</li>
            </ul>
        </div>

        <div class="footer">
            <p>Bible Picture Story Display v1.0 | Built with HTML5, CSS3, and JavaScript</p>
            <p>No installation required - runs entirely in your web browser</p>
        </div>
    </div>

    <script>
        function downloadSample() {
            // Create a link to download the sample story file
            const link = document.createElement('a');
            link.href = 'sample-story.json';
            link.download = 'sample-david-goliath-story.json';
            link.click();
        }

        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
