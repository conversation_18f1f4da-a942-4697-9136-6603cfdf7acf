<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bible Picture Story Display - Functionality Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        
        .test-item.pass {
            background-color: #d4edda;
            border-left-color: #28a745;
        }
        
        .test-item.fail {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .test-item.pending {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .status-icon {
            margin-right: 1rem;
            font-size: 1.2rem;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            margin: 0.25rem;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-test {
            background-color: #28a745;
        }
        
        .btn-test:hover {
            background-color: #1e7e34;
        }
        
        h1, h2 {
            color: #333;
        }
        
        .instructions {
            background-color: #e9ecef;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Bible Picture Story Display - Functionality Test</h1>
        <div class="instructions">
            <strong>Instructions:</strong> Click the test buttons below to verify each feature works correctly. 
            This will help ensure the application is functioning properly before your presentation.
        </div>
    </div>

    <div class="test-container">
        <h2>Core Application Tests</h2>
        
        <div class="test-item pending" id="test-control-panel">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>Control Panel Loading</strong>
                <div>Verify the control panel interface loads correctly</div>
            </div>
            <button class="btn btn-test" onclick="testControlPanel()">Test</button>
        </div>
        
        <div class="test-item pending" id="test-projector">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>Projector Window</strong>
                <div>Test opening and communication with projector display</div>
            </div>
            <button class="btn btn-test" onclick="testProjector()">Test</button>
        </div>
        
        <div class="test-item pending" id="test-sample-import">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>Sample Story Import</strong>
                <div>Test importing the sample David and Goliath story</div>
            </div>
            <button class="btn btn-test" onclick="testSampleImport()">Test</button>
        </div>
        
        <div class="test-item pending" id="test-local-storage">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>Local Storage</strong>
                <div>Verify browser can save and load data</div>
            </div>
            <button class="btn btn-test" onclick="testLocalStorage()">Test</button>
        </div>
    </div>

    <div class="test-container">
        <h2>Feature Tests</h2>
        
        <div class="test-item pending" id="test-image-upload">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>Image Upload Support</strong>
                <div>Check if File API is available for image uploads</div>
            </div>
            <button class="btn btn-test" onclick="testImageUpload()">Test</button>
        </div>
        
        <div class="test-item pending" id="test-postmessage">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>Window Communication</strong>
                <div>Verify postMessage API is available</div>
            </div>
            <button class="btn btn-test" onclick="testPostMessage()">Test</button>
        </div>
        
        <div class="test-item pending" id="test-fullscreen">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>Fullscreen API</strong>
                <div>Check fullscreen capabilities for projector</div>
            </div>
            <button class="btn btn-test" onclick="testFullscreen()">Test</button>
        </div>
        
        <div class="test-item pending" id="test-css-transforms">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>CSS Transforms</strong>
                <div>Verify animation support for zoom and pan</div>
            </div>
            <button class="btn btn-test" onclick="testCSSTransforms()">Test</button>
        </div>
    </div>

    <div class="test-container">
        <h2>Browser Compatibility</h2>
        
        <div class="test-item pending" id="test-es6">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>ES6+ Support</strong>
                <div>Check for modern JavaScript features</div>
            </div>
            <button class="btn btn-test" onclick="testES6()">Test</button>
        </div>
        
        <div class="test-item pending" id="test-css-grid">
            <span class="status-icon">⏳</span>
            <div style="flex: 1;">
                <strong>CSS Grid Support</strong>
                <div>Verify layout compatibility</div>
            </div>
            <button class="btn btn-test" onclick="testCSSGrid()">Test</button>
        </div>
    </div>

    <div class="test-container">
        <h2>Quick Actions</h2>
        <button class="btn" onclick="runAllTests()">🚀 Run All Tests</button>
        <button class="btn" onclick="openControlPanel()">📋 Open Control Panel</button>
        <button class="btn" onclick="openGettingStarted()">📖 Getting Started Guide</button>
        <button class="btn" onclick="downloadSample()">📥 Download Sample Story</button>
    </div>

    <script>
        function updateTestStatus(testId, status, message = '') {
            const testItem = document.getElementById(testId);
            const statusIcon = testItem.querySelector('.status-icon');
            
            testItem.className = `test-item ${status}`;
            
            switch(status) {
                case 'pass':
                    statusIcon.textContent = '✅';
                    break;
                case 'fail':
                    statusIcon.textContent = '❌';
                    if (message) {
                        const messageDiv = testItem.querySelector('div div');
                        messageDiv.textContent += ` - ${message}`;
                    }
                    break;
                case 'pending':
                    statusIcon.textContent = '⏳';
                    break;
            }
        }

        function testControlPanel() {
            try {
                // Test if we can access the control panel
                const testWindow = window.open('index.html', '_blank', 'width=400,height=300');
                if (testWindow) {
                    updateTestStatus('test-control-panel', 'pass');
                    setTimeout(() => testWindow.close(), 2000);
                } else {
                    updateTestStatus('test-control-panel', 'fail', 'Pop-up blocked');
                }
            } catch (error) {
                updateTestStatus('test-control-panel', 'fail', error.message);
            }
        }

        function testProjector() {
            try {
                const testWindow = window.open('projector.html', '_blank', 'width=400,height=300');
                if (testWindow) {
                    updateTestStatus('test-projector', 'pass');
                    setTimeout(() => testWindow.close(), 2000);
                } else {
                    updateTestStatus('test-projector', 'fail', 'Pop-up blocked');
                }
            } catch (error) {
                updateTestStatus('test-projector', 'fail', error.message);
            }
        }

        function testSampleImport() {
            fetch('sample-story.json')
                .then(response => response.json())
                .then(data => {
                    if (data.slides && data.slides.length > 0) {
                        updateTestStatus('test-sample-import', 'pass');
                    } else {
                        updateTestStatus('test-sample-import', 'fail', 'Invalid sample data');
                    }
                })
                .catch(error => {
                    updateTestStatus('test-sample-import', 'fail', 'File not found');
                });
        }

        function testLocalStorage() {
            try {
                const testKey = 'bibleStoryTest';
                const testData = { test: true };
                localStorage.setItem(testKey, JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                localStorage.removeItem(testKey);
                
                if (retrieved && retrieved.test) {
                    updateTestStatus('test-local-storage', 'pass');
                } else {
                    updateTestStatus('test-local-storage', 'fail', 'Data corruption');
                }
            } catch (error) {
                updateTestStatus('test-local-storage', 'fail', 'Not available');
            }
        }

        function testImageUpload() {
            if (window.File && window.FileReader && window.FileList && window.Blob) {
                updateTestStatus('test-image-upload', 'pass');
            } else {
                updateTestStatus('test-image-upload', 'fail', 'File API not supported');
            }
        }

        function testPostMessage() {
            if (window.postMessage) {
                updateTestStatus('test-postmessage', 'pass');
            } else {
                updateTestStatus('test-postmessage', 'fail', 'PostMessage not supported');
            }
        }

        function testFullscreen() {
            if (document.documentElement.requestFullscreen || 
                document.documentElement.webkitRequestFullscreen || 
                document.documentElement.mozRequestFullScreen) {
                updateTestStatus('test-fullscreen', 'pass');
            } else {
                updateTestStatus('test-fullscreen', 'fail', 'Fullscreen API not supported');
            }
        }

        function testCSSTransforms() {
            const testElement = document.createElement('div');
            testElement.style.transform = 'scale(1.5) translate(10px, 10px)';
            if (testElement.style.transform) {
                updateTestStatus('test-css-transforms', 'pass');
            } else {
                updateTestStatus('test-css-transforms', 'fail', 'CSS transforms not supported');
            }
        }

        function testES6() {
            try {
                // Test arrow functions, const/let, template literals
                const test = () => `ES6 ${2021}`;
                if (test() === 'ES6 2021') {
                    updateTestStatus('test-es6', 'pass');
                } else {
                    updateTestStatus('test-es6', 'fail', 'ES6 features not working');
                }
            } catch (error) {
                updateTestStatus('test-es6', 'fail', 'ES6 not supported');
            }
        }

        function testCSSGrid() {
            const testElement = document.createElement('div');
            testElement.style.display = 'grid';
            if (testElement.style.display === 'grid') {
                updateTestStatus('test-css-grid', 'pass');
            } else {
                updateTestStatus('test-css-grid', 'fail', 'CSS Grid not supported');
            }
        }

        function runAllTests() {
            const tests = [
                testControlPanel,
                testProjector,
                testSampleImport,
                testLocalStorage,
                testImageUpload,
                testPostMessage,
                testFullscreen,
                testCSSTransforms,
                testES6,
                testCSSGrid
            ];

            tests.forEach((test, index) => {
                setTimeout(() => test(), index * 500);
            });
        }

        function openControlPanel() {
            window.open('index.html', '_blank');
        }

        function openGettingStarted() {
            window.open('getting-started.html', '_blank');
        }

        function downloadSample() {
            const link = document.createElement('a');
            link.href = 'sample-story.json';
            link.download = 'sample-david-goliath-story.json';
            link.click();
        }

        // Auto-run basic compatibility tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testLocalStorage();
                testImageUpload();
                testPostMessage();
                testFullscreen();
                testCSSTransforms();
                testES6();
                testCSSGrid();
            }, 1000);
        });
    </script>
</body>
</html>
