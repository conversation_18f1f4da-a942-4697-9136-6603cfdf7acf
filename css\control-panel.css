/* Control Panel Dark Theme Styles */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --accent-primary: #4a90e2;
    --accent-secondary: #5cb85c;
    --accent-danger: #d9534f;
    --border-color: #444444;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    --border-radius: 6px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    background-color: var(--bg-secondary);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

.header-left h1 {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

.header-left .subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.header-right {
    display: flex;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #357abd;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: #4a4a4a;
}

.btn-nav {
    background-color: var(--accent-secondary);
    color: white;
    padding: 0.75rem 1rem;
}

.btn-nav:hover:not(:disabled) {
    background-color: #449d44;
}

.btn-nav:disabled {
    background-color: var(--text-muted);
    cursor: not-allowed;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-animation {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 0.5rem;
    margin: 0.25rem;
}

.btn-animation:hover {
    background-color: #4a4a4a;
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.left-panel, .right-panel {
    width: 300px;
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
}

.right-panel {
    border-right: none;
    border-left: 1px solid var(--border-color);
}

.center-panel {
    flex: 1;
    background-color: var(--bg-primary);
    display: flex;
    flex-direction: column;
}

/* Panel Sections */
.panel-section {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.panel-section h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.upload-area:hover, .upload-area.dragover {
    border-color: var(--accent-primary);
    background-color: rgba(74, 144, 226, 0.1);
}

.upload-placeholder i {
    font-size: 2rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.upload-placeholder p {
    color: var(--text-secondary);
}

/* Image List */
.image-list {
    margin-top: 1rem;
}

.image-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background-color: var(--bg-tertiary);
    margin-bottom: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
}

.image-item:hover {
    background-color: #4a4a4a;
}

.image-item.active {
    background-color: var(--accent-primary);
}

.image-item.dragging {
    opacity: 0.5;
}

.image-thumbnail {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 0.5rem;
}

.image-info {
    flex: 1;
}

.image-name {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.image-size {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.image-actions {
    display: flex;
    gap: 0.25rem;
}

.image-actions button {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
}

.image-actions button:hover {
    color: var(--text-primary);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Navigation Controls */
.nav-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.slide-counter {
    font-weight: bold;
    color: var(--text-secondary);
}

.jump-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.jump-controls input {
    width: 60px;
    padding: 0.25rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-primary);
}

/* Preview */
.preview-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 1rem;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.preview-area {
    flex: 1;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.preview-placeholder {
    text-align: center;
    color: var(--text-muted);
}

.preview-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Text Controls */
.text-controls textarea {
    width: 100%;
    height: 100px;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    padding: 0.5rem;
    resize: vertical;
    margin-bottom: 1rem;
}

.text-formatting {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.format-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.format-row label {
    min-width: 80px;
    font-size: 0.9rem;
}

.format-btn {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.format-btn:hover {
    background-color: #4a4a4a;
}

.format-btn.active {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
}

/* Animation Controls */
.animation-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.animation-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.animation-group label {
    font-weight: bold;
    color: var(--text-secondary);
}

.control-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.pan-controls {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 0.25rem;
    max-width: 150px;
}

.pan-controls #panUpBtn {
    grid-column: 2;
    grid-row: 1;
}

.pan-middle {
    grid-column: 1 / -1;
    grid-row: 2;
    display: flex;
    gap: 0.25rem;
}

.pan-controls #panDownBtn {
    grid-column: 2;
    grid-row: 3;
}

/* Status Bar */
.status-bar {
    background-color: var(--bg-secondary);
    padding: 0.5rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.status-left, .status-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-connected {
    color: var(--accent-secondary);
}

.status-disconnected {
    color: var(--accent-danger);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--bg-secondary);
    margin: 15% auto;
    padding: 2rem;
    border-radius: var(--border-radius);
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
}

.close:hover {
    color: var(--text-primary);
}

.shortcuts-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
}

.shortcut-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

kbd {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    font-family: monospace;
    font-size: 0.9rem;
    min-width: 60px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .left-panel, .right-panel {
        width: 250px;
    }
}

@media (max-width: 900px) {
    .main-content {
        flex-direction: column;
    }
    
    .left-panel, .right-panel {
        width: 100%;
        height: 200px;
    }
    
    .center-panel {
        flex: 1;
        min-height: 300px;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
