# Bible Picture Story Display - PowerShell Server Launcher
param(
    [int]$Port = 8000
)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " Bible Picture Story Display Server" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to test if a port is available
function Test-Port {
    param([int]$Port)
    try {
        $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $Port)
        $listener.Start()
        $listener.Stop()
        return $true
    }
    catch {
        return $false
    }
}

# Function to open browser
function Open-Browser {
    param([string]$Url)
    try {
        Start-Process $Url
        Write-Host "Browser opened automatically!" -ForegroundColor Green
    }
    catch {
        Write-Host "Please manually open your browser and go to: $Url" -ForegroundColor Yellow
    }
}

# Find an available port
$originalPort = $Port
while (-not (Test-Port -Port $Port)) {
    $Port++
    if ($Port -gt ($originalPort + 10)) {
        Write-Host "Could not find an available port. Please close other applications and try again." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "Starting local web server..." -ForegroundColor Green
Write-Host ""

# Try Python first
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Python found: $pythonVersion" -ForegroundColor Green
        Write-Host ""
        Write-Host "Server will be available at: http://localhost:$Port" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Instructions:" -ForegroundColor Cyan
        Write-Host "1. Keep this window open while using the app" -ForegroundColor White
        Write-Host "2. Your browser should open automatically" -ForegroundColor White
        Write-Host "3. Click on 'getting-started.html' to begin" -ForegroundColor White
        Write-Host "4. Press Ctrl+C to stop the server" -ForegroundColor White
        Write-Host ""
        Write-Host "Starting server now..." -ForegroundColor Green
        Write-Host ""
        
        # Open browser after a short delay
        Start-Job -ScriptBlock { 
            param($url)
            Start-Sleep 2
            Start-Process $url
        } -ArgumentList "http://localhost:$Port"
        
        # Start Python server
        python -m http.server $Port
        exit 0
    }
}
catch {
    # Python not found, continue to next option
}

# Try Node.js
try {
    $nodeVersion = node --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Node.js found: $nodeVersion" -ForegroundColor Green
        Write-Host ""
        
        # Check for http-server
        try {
            npx http-server --version 2>&1 | Out-Null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Server will be available at: http://localhost:$Port" -ForegroundColor Yellow
                Write-Host ""
                Write-Host "Instructions:" -ForegroundColor Cyan
                Write-Host "1. Keep this window open while using the app" -ForegroundColor White
                Write-Host "2. Your browser should open automatically" -ForegroundColor White
                Write-Host "3. Click on 'getting-started.html' to begin" -ForegroundColor White
                Write-Host "4. Press Ctrl+C to stop the server" -ForegroundColor White
                Write-Host ""
                Write-Host "Starting server now..." -ForegroundColor Green
                Write-Host ""
                
                # Start Node.js server with auto-open
                npx http-server -p $Port -o
                exit 0
            }
        }
        catch {
            Write-Host "Installing http-server..." -ForegroundColor Yellow
            npm install -g http-server
            if ($LASTEXITCODE -eq 0) {
                npx http-server -p $Port -o
                exit 0
            }
        }
    }
}
catch {
    # Node.js not found, continue to next option
}

# Try PowerShell built-in web server (Windows 10/11 with .NET)
try {
    Write-Host "Attempting to use PowerShell built-in server..." -ForegroundColor Yellow
    
    $listener = New-Object System.Net.HttpListener
    $listener.Prefixes.Add("http://localhost:$Port/")
    $listener.Start()
    
    Write-Host ""
    Write-Host "Server started successfully!" -ForegroundColor Green
    Write-Host "Server available at: http://localhost:$Port" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Instructions:" -ForegroundColor Cyan
    Write-Host "1. Keep this window open while using the app" -ForegroundColor White
    Write-Host "2. Open your browser and go to: http://localhost:$Port" -ForegroundColor White
    Write-Host "3. Click on 'getting-started.html' to begin" -ForegroundColor White
    Write-Host "4. Press Ctrl+C to stop the server" -ForegroundColor White
    Write-Host ""
    
    # Open browser
    Open-Browser "http://localhost:$Port"
    
    Write-Host "Server is running... Press Ctrl+C to stop." -ForegroundColor Green
    Write-Host ""
    
    # Simple file server loop
    while ($listener.IsListening) {
        try {
            $context = $listener.GetContext()
            $request = $context.Request
            $response = $context.Response
            
            $localPath = $request.Url.LocalPath
            if ($localPath -eq "/") { $localPath = "/getting-started.html" }
            
            $filePath = Join-Path $PWD $localPath.TrimStart('/')
            
            if (Test-Path $filePath -PathType Leaf) {
                $content = [System.IO.File]::ReadAllBytes($filePath)
                $response.ContentLength64 = $content.Length
                
                # Set content type
                $extension = [System.IO.Path]::GetExtension($filePath).ToLower()
                switch ($extension) {
                    ".html" { $response.ContentType = "text/html" }
                    ".css" { $response.ContentType = "text/css" }
                    ".js" { $response.ContentType = "application/javascript" }
                    ".json" { $response.ContentType = "application/json" }
                    ".png" { $response.ContentType = "image/png" }
                    ".jpg" { $response.ContentType = "image/jpeg" }
                    ".jpeg" { $response.ContentType = "image/jpeg" }
                    ".gif" { $response.ContentType = "image/gif" }
                    ".svg" { $response.ContentType = "image/svg+xml" }
                    default { $response.ContentType = "application/octet-stream" }
                }
                
                $response.OutputStream.Write($content, 0, $content.Length)
            }
            else {
                $response.StatusCode = 404
                $errorContent = [System.Text.Encoding]::UTF8.GetBytes("File not found: $localPath")
                $response.OutputStream.Write($errorContent, 0, $errorContent.Length)
            }
            
            $response.Close()
        }
        catch {
            if ($_.Exception.Message -notlike "*operation was canceled*") {
                Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
            }
            break
        }
    }
    
    $listener.Stop()
    exit 0
}
catch {
    Write-Host "PowerShell server failed: $($_.Exception.Message)" -ForegroundColor Red
}

# If all else fails, show installation instructions
Write-Host ""
Write-Host "==========================================" -ForegroundColor Red
Write-Host " NO COMPATIBLE SERVER FOUND" -ForegroundColor Red
Write-Host "==========================================" -ForegroundColor Red
Write-Host ""
Write-Host "To run this application, please install one of the following:" -ForegroundColor Yellow
Write-Host ""
Write-Host "OPTION 1 - Python (Recommended):" -ForegroundColor Cyan
Write-Host "  1. Download from: https://www.python.org/downloads/" -ForegroundColor White
Write-Host "  2. Install Python (check 'Add to PATH')" -ForegroundColor White
Write-Host "  3. Run this script again" -ForegroundColor White
Write-Host ""
Write-Host "OPTION 2 - Node.js:" -ForegroundColor Cyan
Write-Host "  1. Download from: https://nodejs.org/" -ForegroundColor White
Write-Host "  2. Install Node.js" -ForegroundColor White
Write-Host "  3. Run this script again" -ForegroundColor White
Write-Host ""
Write-Host "OPTION 3 - Alternative servers:" -ForegroundColor Cyan
Write-Host "  - XAMPP: https://www.apachefriends.org/" -ForegroundColor White
Write-Host "  - Live Server (VS Code extension)" -ForegroundColor White
Write-Host "  - Any other local web server" -ForegroundColor White
Write-Host ""
Write-Host "==========================================" -ForegroundColor Red
Write-Host ""

Read-Host "Press Enter to exit"
