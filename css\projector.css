/* Projector Display Styles */
:root {
    --bg-black: #000000;
    --text-white: #ffffff;
    --text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    --transition-duration: 0.5s;
    --status-bg: rgba(0, 0, 0, 0.7);
    --status-connected: #28a745;
    --status-connecting: #ffc107;
    --status-disconnected: #dc3545;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-black);
    color: var(--text-white);
    height: 100vh;
    overflow: hidden;
    cursor: none; /* Hide cursor for clean presentation */
}

.projector-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Main Display Area */
.display-area {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-black);
}

.image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

#currentImage {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform var(--transition-duration) ease-in-out,
                opacity var(--transition-duration) ease-in-out;
    transform-origin: center center;
}

/* Image Animation States */
.image-zoom-in {
    transform: scale(1.2);
}

.image-zoom-out {
    transform: scale(0.8);
}

.image-pan-left {
    transform: translateX(-10%);
}

.image-pan-right {
    transform: translateX(10%);
}

.image-pan-up {
    transform: translateY(-10%);
}

.image-pan-down {
    transform: translateY(10%);
}

.image-pan-center {
    transform: translate(0, 0) scale(1);
}

/* Placeholder */
.placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

.placeholder-content {
    text-align: center;
    animation: fadeInUp 1s ease-out;
}

.cross-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
    color: #ffd700;
    text-shadow: var(--text-shadow);
}

.placeholder h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: var(--text-shadow);
    font-weight: 300;
}

.placeholder p {
    font-size: 1.5rem;
    opacity: 0.8;
    text-shadow: var(--text-shadow);
}

/* Text Overlay */
.text-overlay {
    position: absolute;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    max-width: 90%;
    text-align: center;
    z-index: 10;
    pointer-events: none;
}

.text-content {
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem 2rem;
    border-radius: 8px;
    backdrop-filter: blur(5px);
    animation: slideInUp 0.5s ease-out;
}

.text-content p {
    margin: 0;
    line-height: 1.4;
    text-shadow: var(--text-shadow);
}

/* Text Alignment Classes */
.text-left {
    text-align: left;
    left: 5%;
    transform: none;
}

.text-center {
    text-align: center;
    left: 50%;
    transform: translateX(-50%);
}

.text-right {
    text-align: right;
    right: 5%;
    left: auto;
    transform: none;
}

/* Loading Indicator */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 100;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--text-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Connection Status */
.connection-status {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--status-bg);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    z-index: 50;
    transition: opacity 0.3s ease;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-icon {
    font-size: 0.8rem;
}

.status-connected .status-icon {
    color: var(--status-connected);
}

.status-connecting .status-icon {
    color: var(--status-connecting);
    animation: pulse 1s infinite;
}

.status-disconnected .status-icon {
    color: var(--status-disconnected);
}

/* Slide Information */
.slide-info {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: var(--status-bg);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    z-index: 50;
    opacity: 0.7;
}

/* Transition Overlay */
.transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-black);
    z-index: 1000;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.transition-overlay.active {
    opacity: 1;
    pointer-events: all;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Slide Transition Effects */
.slide-transition-fade {
    animation: fadeTransition var(--transition-duration) ease-in-out;
}

.slide-transition-slide-left {
    animation: slideLeftTransition var(--transition-duration) ease-in-out;
}

.slide-transition-slide-right {
    animation: slideRightTransition var(--transition-duration) ease-in-out;
}

@keyframes fadeTransition {
    0% { opacity: 1; }
    50% { opacity: 0; }
    100% { opacity: 1; }
}

@keyframes slideLeftTransition {
    0% { transform: translateX(0); }
    50% { transform: translateX(-100%); }
    51% { transform: translateX(100%); }
    100% { transform: translateX(0); }
}

@keyframes slideRightTransition {
    0% { transform: translateX(0); }
    50% { transform: translateX(100%); }
    51% { transform: translateX(-100%); }
    100% { transform: translateX(0); }
}

/* Fullscreen optimizations */
.projector-container:-webkit-full-screen {
    background-color: var(--bg-black);
}

.projector-container:-moz-full-screen {
    background-color: var(--bg-black);
}

.projector-container:fullscreen {
    background-color: var(--bg-black);
}

/* Hide status elements during presentation */
.presentation-mode .connection-status,
.presentation-mode .slide-info {
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .placeholder h1 {
        font-size: 2rem;
    }
    
    .placeholder p {
        font-size: 1.2rem;
    }
    
    .cross-icon {
        font-size: 3rem;
    }
    
    .text-overlay {
        max-width: 95%;
        bottom: 5%;
    }
    
    .text-content {
        padding: 0.75rem 1.5rem;
    }
}

@media (max-height: 600px) {
    .placeholder h1 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .placeholder p {
        font-size: 1.2rem;
    }
    
    .cross-icon {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
    }
}

/* Print styles (hide everything for clean printing) */
@media print {
    .connection-status,
    .slide-info,
    .loading-indicator {
        display: none !important;
    }
}
