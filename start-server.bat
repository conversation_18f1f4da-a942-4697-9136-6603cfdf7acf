@echo off
title Bible Picture Story Display - Local Server
echo.
echo ========================================
echo  Bible Picture Story Display Server
echo ========================================
echo.
echo Starting local web server...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python found! Starting server with Python...
    echo.
    echo Server will be available at: http://localhost:8000
    echo.
    echo Instructions:
    echo 1. Keep this window open while using the app
    echo 2. Open your web browser and go to: http://localhost:8000
    echo 3. Click on "getting-started.html" to begin
    echo 4. Press Ctrl+C in this window to stop the server
    echo.
    echo Starting server now...
    echo.
    
    REM Try Python 3 first, then Python 2
    python -m http.server 8000 2>nul
    if %errorlevel% neq 0 (
        python -m SimpleHTTPServer 8000
    )
    goto :end
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo Node.js found! Checking for http-server...
    
    REM Check if http-server is installed globally
    npx http-server --version >nul 2>&1
    if %errorlevel% == 0 (
        echo Starting server with Node.js http-server...
        echo.
        echo Server will be available at: http://localhost:8080
        echo.
        echo Instructions:
        echo 1. Keep this window open while using the app
        echo 2. Open your web browser and go to: http://localhost:8080
        echo 3. Click on "getting-started.html" to begin
        echo 4. Press Ctrl+C in this window to stop the server
        echo.
        echo Starting server now...
        echo.
        npx http-server -p 8080 -o
        goto :end
    ) else (
        echo Installing http-server...
        npm install -g http-server
        if %errorlevel% == 0 (
            echo Starting server with Node.js http-server...
            echo.
            echo Server will be available at: http://localhost:8080
            echo.
            npx http-server -p 8080 -o
            goto :end
        )
    )
)

REM If neither Python nor Node.js is available, provide instructions
echo.
echo ==========================================
echo  NO COMPATIBLE SERVER FOUND
echo ==========================================
echo.
echo To run this application, you need either:
echo.
echo OPTION 1 - Python (Recommended):
echo   1. Download Python from: https://www.python.org/downloads/
echo   2. Install Python (make sure to check "Add to PATH")
echo   3. Run this script again
echo.
echo OPTION 2 - Node.js:
echo   1. Download Node.js from: https://nodejs.org/
echo   2. Install Node.js
echo   3. Run this script again
echo.
echo OPTION 3 - Use a different web server:
echo   - XAMPP: https://www.apachefriends.org/
echo   - WAMP: http://www.wampserver.com/
echo   - Live Server (VS Code extension)
echo.
echo OPTION 4 - Online hosting:
echo   - Upload files to any web hosting service
echo   - GitHub Pages, Netlify, Vercel, etc.
echo.
echo ==========================================
echo.

:end
echo.
echo Press any key to exit...
pause >nul
