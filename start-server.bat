@echo off
title Bible Picture Story Display - Local Server
color 0A
echo.
echo ========================================
echo  Bible Picture Story Display Server
echo ========================================
echo.
echo Starting local web server...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo [SUCCESS] Python found! Starting server...
    echo.
    echo Server will be available at: http://localhost:8000
    echo.
    echo Instructions:
    echo 1. Keep this window open while using the app
    echo 2. <PERSON><PERSON><PERSON> will open automatically in 3 seconds
    echo 3. If browser doesn't open, go to: http://localhost:8000
    echo 4. Press Ctrl+C to stop the server
    echo.
    echo [INFO] Starting Python HTTP server...
    echo.

    REM Start the server in background
    start /B python -m http.server 8000

    REM Wait for server to start
    echo [INFO] Waiting for server to initialize...
    timeout /t 3 /nobreak >nul

    REM Test if server is running
    curl -s http://localhost:8000 >nul 2>&1
    if %errorlevel% == 0 (
        echo [SUCCESS] Server is running!
        echo [INFO] Opening browser...
        start http://localhost:8000/getting-started.html
    ) else (
        echo [WARNING] Server may still be starting...
        echo [INFO] Opening browser anyway...
        start http://localhost:8000/getting-started.html
    )

    REM Keep the window open and show server status
    echo.
    echo ========================================
    echo  SERVER IS RUNNING SUCCESSFULLY
    echo ========================================
    echo.
    echo [SUCCESS] Browser should have opened automatically!
    echo [INFO] If browser didn't open, go to: http://localhost:8000
    echo.
    echo [CONTROLS]
    echo - Press Ctrl+C to stop the server
    echo - Press any key to open browser again
    echo - Close this window to stop the server
    echo.

    :wait_loop
    pause >nul
    echo [INFO] Opening browser...
    start http://localhost:8000/getting-started.html
    goto wait_loop
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo Node.js found! Starting server with Node.js...
    echo.
    echo Server will be available at: http://localhost:8080
    echo.
    echo Instructions:
    echo 1. Keep this window open while using the app
    echo 2. Browser will open automatically
    echo 3. If browser doesn't open, go to: http://localhost:8080
    echo 4. Press Ctrl+C in this window to stop the server
    echo.
    echo Starting server now...
    echo.

    REM Try to use http-server if available, otherwise use a simple alternative
    npx http-server --version >nul 2>&1
    if %errorlevel% == 0 (
        echo Using http-server...
        start /B npx http-server -p 8080 -s
        timeout /t 3 /nobreak >nul
        start http://localhost:8080/getting-started.html
    ) else (
        echo Installing http-server...
        npm install -g http-server >nul 2>&1
        if %errorlevel% == 0 (
            start /B npx http-server -p 8080 -s
            timeout /t 3 /nobreak >nul
            start http://localhost:8080/getting-started.html
        ) else (
            echo Failed to install http-server, trying alternative...
            start /B node -e "const http=require('http'),fs=require('fs'),path=require('path');http.createServer((req,res)=>{const file=path.join(__dirname,req.url==='/'?'/getting-started.html':req.url);fs.readFile(file,(err,data)=>{if(err){res.writeHead(404);res.end('Not found');return;}const ext=path.extname(file);const contentType={'html':'text/html','css':'text/css','js':'application/javascript','json':'application/json','png':'image/png','jpg':'image/jpeg','gif':'image/gif'}[ext.slice(1)]||'text/plain';res.writeHead(200,{'Content-Type':contentType});res.end(data);});}).listen(8080,()=>console.log('Server running on port 8080'));"
            timeout /t 3 /nobreak >nul
            start http://localhost:8080
        )
    )

    echo.
    echo ========================================
    echo  SERVER IS RUNNING
    echo ========================================
    echo.
    echo Browser should have opened automatically!
    echo If not, manually go to: http://localhost:8080
    echo.
    echo Press Ctrl+C to stop the server
    echo Press any key to open browser again
    echo.

    :wait_loop_node
    pause >nul
    start http://localhost:8080/getting-started.html
    goto wait_loop_node
)

REM If neither Python nor Node.js is available, provide instructions
echo.
echo ==========================================
echo  NO COMPATIBLE SERVER FOUND
echo ==========================================
echo.
echo To run this application, you need either:
echo.
echo OPTION 1 - Python (Recommended):
echo   1. Download Python from: https://www.python.org/downloads/
echo   2. Install Python (make sure to check "Add to PATH")
echo   3. Run this script again
echo.
echo OPTION 2 - Node.js:
echo   1. Download Node.js from: https://nodejs.org/
echo   2. Install Node.js
echo   3. Run this script again
echo.
echo OPTION 3 - Use a different web server:
echo   - XAMPP: https://www.apachefriends.org/
echo   - WAMP: http://www.wampserver.com/
echo   - Live Server (VS Code extension)
echo.
echo OPTION 4 - Online hosting:
echo   - Upload files to any web hosting service
echo   - GitHub Pages, Netlify, Vercel, etc.
echo.
echo ==========================================
echo.

:end
echo.
echo Press any key to exit...
pause >nul
