// Bible Picture Story Display - Projector JavaScript
class BibleStoryProjector {
    constructor() {
        this.stories = [];
        this.currentSlideIndex = 0;
        this.animationSettings = {
            speed: 1.0,
            duration: 1000
        };
        this.isConnected = false;
        this.currentTransform = {
            scale: 1,
            translateX: 0,
            translateY: 0
        };
        
        this.initializeElements();
        this.bindEvents();
        this.setupCommunication();
        this.enterPresentationMode();
    }

    initializeElements() {
        this.displayArea = document.getElementById('displayArea');
        this.imageContainer = document.getElementById('imageContainer');
        this.currentImage = document.getElementById('currentImage');
        this.placeholder = document.getElementById('placeholder');
        this.textOverlay = document.getElementById('textOverlay');
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.connectionStatus = document.getElementById('connectionStatus');
        this.slideInfo = document.getElementById('slideInfo');
        this.slideNumber = document.getElementById('slideNumber');
        this.transitionOverlay = document.getElementById('transitionOverlay');
    }

    bindEvents() {
        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeydown.bind(this));
        
        // Window events
        window.addEventListener('resize', this.handleResize.bind(this));
        window.addEventListener('beforeunload', () => {
            this.sendMessage('DISCONNECTED', {});
        });
        
        // Image load events
        this.currentImage.addEventListener('load', this.handleImageLoad.bind(this));
        this.currentImage.addEventListener('error', this.handleImageError.bind(this));
        
        // Fullscreen events
        document.addEventListener('fullscreenchange', this.handleFullscreenChange.bind(this));
        document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange.bind(this));
        document.addEventListener('mozfullscreenchange', this.handleFullscreenChange.bind(this));
    }

    setupCommunication() {
        window.addEventListener('message', this.handleMessage.bind(this));
        
        // Send ready signal to control panel
        if (window.opener) {
            this.sendMessage('READY', {});
            this.updateConnectionStatus('connected');
        } else {
            this.updateConnectionStatus('disconnected');
        }
    }

    sendMessage(type, data) {
        if (window.opener && !window.opener.closed) {
            window.opener.postMessage({ type, data }, '*');
        }
    }

    handleMessage(event) {
        if (event.source === window.opener) {
            const { type, data } = event.data;
            
            switch (type) {
                case 'INIT':
                    this.handleInit(data);
                    break;
                case 'SLIDE_CHANGE':
                    this.handleSlideChange(data);
                    break;
                case 'TEXT_UPDATE':
                    this.handleTextUpdate(data);
                    break;
                case 'ANIMATION':
                    this.handleAnimation(data);
                    break;
                case 'TOGGLE_FULLSCREEN':
                    this.toggleFullscreen();
                    break;
            }
        }
    }

    handleInit(data) {
        this.stories = data.stories || [];
        this.currentSlideIndex = data.currentIndex || 0;
        this.animationSettings = data.animationSettings || this.animationSettings;
        
        this.updateConnectionStatus('connected');
        
        if (this.stories.length > 0 && this.currentSlideIndex >= 0) {
            this.displaySlide(this.currentSlideIndex);
        } else {
            this.showPlaceholder();
        }
    }

    handleSlideChange(data) {
        this.currentSlideIndex = data.index;
        this.displaySlide(this.currentSlideIndex, true);
    }

    handleTextUpdate(data) {
        this.updateTextOverlay(data.text, data.style);
    }

    handleAnimation(data) {
        this.applyAnimation(data.type, data.speed, data.duration);
    }

    displaySlide(index, withTransition = false) {
        if (index < 0 || index >= this.stories.length) {
            this.showPlaceholder();
            return;
        }

        const slide = this.stories[index];
        this.showLoading();

        if (withTransition) {
            this.transitionOverlay.classList.add('active');
            setTimeout(() => {
                this.loadSlide(slide);
                setTimeout(() => {
                    this.transitionOverlay.classList.remove('active');
                    this.hideLoading();
                }, 150);
            }, 150);
        } else {
            this.loadSlide(slide);
            this.hideLoading();
        }

        this.updateSlideInfo(index + 1, this.stories.length);
    }

    loadSlide(slide) {
        this.placeholder.style.display = 'none';
        this.currentImage.src = slide.src;
        this.currentImage.alt = slide.name;
        this.currentImage.style.display = 'block';
        
        // Reset transform
        this.resetTransform();
        
        // Update text overlay
        this.updateTextOverlay(slide.text, slide.textStyle);
    }

    showPlaceholder() {
        this.currentImage.style.display = 'none';
        this.placeholder.style.display = 'flex';
        this.textOverlay.innerHTML = '';
        this.updateSlideInfo(0, 0);
    }

    updateTextOverlay(text, style) {
        if (!text || text.trim() === '') {
            this.textOverlay.innerHTML = '';
            return;
        }

        const textContent = document.createElement('div');
        textContent.className = 'text-content';
        
        const paragraph = document.createElement('p');
        paragraph.textContent = text;
        paragraph.style.cssText = `
            font-size: ${style.fontSize}px;
            color: ${style.color};
            font-weight: ${style.bold ? 'bold' : 'normal'};
            font-style: ${style.italic ? 'italic' : 'normal'};
            text-decoration: ${style.underline ? 'underline' : 'none'};
            margin: 0;
            line-height: 1.4;
        `;
        
        textContent.appendChild(paragraph);
        
        // Set alignment class
        this.textOverlay.className = `text-overlay text-${style.alignment}`;
        this.textOverlay.innerHTML = '';
        this.textOverlay.appendChild(textContent);
    }

    applyAnimation(type, speed = 1.0, duration = 1000) {
        const adjustedDuration = duration / speed;
        this.currentImage.style.transition = `transform ${adjustedDuration}ms ease-in-out`;
        
        switch (type) {
            case 'zoom-in':
                this.currentTransform.scale = Math.min(this.currentTransform.scale * 1.2, 3);
                break;
            case 'zoom-out':
                this.currentTransform.scale = Math.max(this.currentTransform.scale / 1.2, 0.5);
                break;
            case 'pan-left':
                this.currentTransform.translateX -= 10;
                break;
            case 'pan-right':
                this.currentTransform.translateX += 10;
                break;
            case 'pan-up':
                this.currentTransform.translateY -= 10;
                break;
            case 'pan-down':
                this.currentTransform.translateY += 10;
                break;
            case 'pan-center':
                this.currentTransform.translateX = 0;
                this.currentTransform.translateY = 0;
                break;
            case 'reset':
                this.currentTransform = { scale: 1, translateX: 0, translateY: 0 };
                break;
        }
        
        this.applyTransform();
    }

    applyTransform() {
        const { scale, translateX, translateY } = this.currentTransform;
        this.currentImage.style.transform = `scale(${scale}) translate(${translateX}%, ${translateY}%)`;
    }

    resetTransform() {
        this.currentTransform = { scale: 1, translateX: 0, translateY: 0 };
        this.currentImage.style.transition = 'none';
        this.applyTransform();
        
        // Re-enable transitions after a brief delay
        setTimeout(() => {
            this.currentImage.style.transition = 'transform 1000ms ease-in-out';
        }, 50);
    }

    handleKeydown(e) {
        switch (e.key) {
            case 'Escape':
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                }
                break;
            case 'F11':
                e.preventDefault();
                this.toggleFullscreen();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                this.sendMessage('PREV_SLIDE', {});
                break;
            case 'ArrowRight':
            case ' ':
                e.preventDefault();
                this.sendMessage('NEXT_SLIDE', {});
                break;
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('Error attempting to enable fullscreen:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    handleFullscreenChange() {
        if (document.fullscreenElement) {
            document.body.classList.add('presentation-mode');
        } else {
            document.body.classList.remove('presentation-mode');
        }
    }

    enterPresentationMode() {
        // Auto-enter fullscreen if possible
        setTimeout(() => {
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen().catch(() => {
                    // Fullscreen failed, continue anyway
                });
            }
        }, 1000);
    }

    updateConnectionStatus(status) {
        const statusIndicator = this.connectionStatus.querySelector('.status-indicator');
        const statusText = this.connectionStatus.querySelector('.status-text');
        
        this.connectionStatus.className = `connection-status status-${status}`;
        
        switch (status) {
            case 'connected':
                statusText.textContent = 'Connected to Control Panel';
                this.isConnected = true;
                break;
            case 'connecting':
                statusText.textContent = 'Connecting to Control Panel...';
                this.isConnected = false;
                break;
            case 'disconnected':
                statusText.textContent = 'Disconnected from Control Panel';
                this.isConnected = false;
                break;
        }
        
        // Hide status after a few seconds when connected
        if (status === 'connected') {
            setTimeout(() => {
                this.connectionStatus.style.opacity = '0';
            }, 3000);
        } else {
            this.connectionStatus.style.opacity = '1';
        }
    }

    updateSlideInfo(current, total) {
        this.slideNumber.textContent = `Slide ${current} of ${total}`;
    }

    showLoading() {
        this.loadingIndicator.style.display = 'block';
    }

    hideLoading() {
        this.loadingIndicator.style.display = 'none';
    }

    handleImageLoad() {
        this.hideLoading();
    }

    handleImageError() {
        this.hideLoading();
        console.error('Failed to load image');
        
        // Show error placeholder
        this.currentImage.style.display = 'none';
        this.placeholder.innerHTML = `
            <div class="placeholder-content">
                <i class="cross-icon">⚠</i>
                <h1>Image Load Error</h1>
                <p>Failed to load the current slide image</p>
            </div>
        `;
        this.placeholder.style.display = 'flex';
    }

    handleResize() {
        // Adjust display for different screen sizes
        this.applyTransform();
    }
}

// Initialize the projector when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new BibleStoryProjector();
});
