<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Image Fix Test - Bible Picture Story Display</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        .console-output {
            background-color: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .test-progress {
            background-color: #e9ecef;
            border-radius: 4px;
            padding: 3px;
            margin: 10px 0;
        }
        .progress-bar {
            background-color: #007bff;
            height: 20px;
            border-radius: 2px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Comprehensive Image Fix Test</h1>
    <p>This test will thoroughly verify that the image loading issue has been fixed.</p>
    
    <div class="test-section">
        <h2>🧪 Test Progress</h2>
        <div class="test-progress">
            <div id="progressBar" class="progress-bar" style="width: 0%">0%</div>
        </div>
        <div id="currentTest">Ready to start testing...</div>
    </div>

    <div class="test-section">
        <h2>🚀 Run Tests</h2>
        <button id="startTestBtn" onclick="runComprehensiveTest()">Start Comprehensive Test</button>
        <button onclick="clearResults()">Clear Results</button>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📝 Console Output</h2>
        <div id="consoleOutput" class="console-output">Console output will appear here...</div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <script>
        let projectorWindow = null;
        let testStep = 0;
        let totalSteps = 8;
        let testResults = [];

        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌ ERROR' : type === 'warn' ? '⚠️ WARN' : '📝 LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function updateProgress(step, message) {
            testStep = step;
            const percentage = Math.round((step / totalSteps) * 100);
            const progressBar = document.getElementById('progressBar');
            const currentTest = document.getElementById('currentTest');
            
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '%';
            currentTest.textContent = `Step ${step}/${totalSteps}: ${message}`;
        }

        function addResult(message, type) {
            const container = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            container.appendChild(div);
            
            testResults.push({ message, type, timestamp: new Date() });
        }

        function createTestImages() {
            const images = [];
            
            // Test Image 1: Simple PNG
            const canvas1 = document.createElement('canvas');
            canvas1.width = 400;
            canvas1.height = 300;
            const ctx1 = canvas1.getContext('2d');
            ctx1.fillStyle = '#4CAF50';
            ctx1.fillRect(0, 0, 400, 300);
            ctx1.fillStyle = 'white';
            ctx1.font = 'bold 24px Arial';
            ctx1.textAlign = 'center';
            ctx1.fillText('TEST IMAGE 1', 200, 150);
            images.push({
                name: 'Test Image 1 (PNG)',
                src: canvas1.toDataURL('image/png'),
                type: 'png'
            });
            
            // Test Image 2: JPEG
            const canvas2 = document.createElement('canvas');
            canvas2.width = 600;
            canvas2.height = 400;
            const ctx2 = canvas2.getContext('2d');
            const gradient = ctx2.createRadialGradient(300, 200, 0, 300, 200, 200);
            gradient.addColorStop(0, '#FF5722');
            gradient.addColorStop(1, '#9C27B0');
            ctx2.fillStyle = gradient;
            ctx2.fillRect(0, 0, 600, 400);
            ctx2.fillStyle = 'white';
            ctx2.font = 'bold 28px Arial';
            ctx2.textAlign = 'center';
            ctx2.fillText('TEST IMAGE 2', 300, 200);
            images.push({
                name: 'Test Image 2 (JPEG)',
                src: canvas2.toDataURL('image/jpeg', 0.9),
                type: 'jpeg'
            });
            
            return images;
        }

        async function runComprehensiveTest() {
            const startBtn = document.getElementById('startTestBtn');
            startBtn.disabled = true;
            startBtn.textContent = 'Testing...';
            
            const container = document.getElementById('testResults');
            container.innerHTML = '';
            testResults = [];
            
            try {
                console.log('=== STARTING COMPREHENSIVE IMAGE FIX TEST ===');
                
                // Step 1: Create test images
                updateProgress(1, 'Creating test images...');
                const testImages = createTestImages();
                console.log('Created test images:', testImages.length);
                addResult(`✓ Created ${testImages.length} test images`, 'pass');
                await sleep(500);
                
                // Step 2: Validate test images locally
                updateProgress(2, 'Validating test images...');
                for (let i = 0; i < testImages.length; i++) {
                    const img = testImages[i];
                    const isValid = await validateImageLocally(img.src);
                    if (isValid) {
                        addResult(`✓ ${img.name} validation passed`, 'pass');
                    } else {
                        addResult(`❌ ${img.name} validation failed`, 'fail');
                        throw new Error(`Test image ${i + 1} validation failed`);
                    }
                }
                await sleep(500);
                
                // Step 3: Open projector window
                updateProgress(3, 'Opening projector window...');
                projectorWindow = window.open('projector.html', 'testProjector', 'width=800,height=600');
                if (!projectorWindow) {
                    throw new Error('Failed to open projector window (popup blocked?)');
                }
                addResult('✓ Projector window opened successfully', 'pass');
                await sleep(2000); // Wait for projector to load
                
                // Step 4: Test INIT message
                updateProgress(4, 'Testing INIT message...');
                const initData = {
                    stories: testImages.map((img, index) => ({
                        id: Date.now() + index,
                        name: img.name,
                        src: img.src,
                        text: `Test text for ${img.name}`,
                        textStyle: {
                            fontSize: 24,
                            color: '#ffffff',
                            bold: true,
                            italic: false,
                            underline: false,
                            alignment: 'center'
                        }
                    })),
                    currentIndex: 0,
                    animationSettings: { speed: 1.0, duration: 500 }
                };
                
                projectorWindow.postMessage({ type: 'INIT', data: initData }, '*');
                addResult('✓ INIT message sent to projector', 'pass');
                await sleep(1000);
                
                // Step 5-7: Test each image
                for (let i = 0; i < testImages.length; i++) {
                    updateProgress(5 + i, `Testing image ${i + 1}...`);
                    
                    const slideData = {
                        slide: initData.stories[i],
                        index: i
                    };
                    
                    console.log(`Testing slide ${i + 1}:`, slideData);
                    projectorWindow.postMessage({ type: 'SLIDE_CHANGE', data: slideData }, '*');
                    addResult(`✓ Sent ${testImages[i].name} to projector`, 'pass');
                    await sleep(1500); // Wait for image to load
                }
                
                // Step 8: Final validation
                updateProgress(8, 'Completing test...');
                addResult('✓ All test images sent successfully', 'pass');
                addResult('🎉 Test completed! Check the projector window to verify images are displayed correctly.', 'info');
                
                // Summary
                const passCount = testResults.filter(r => r.type === 'pass').length;
                const failCount = testResults.filter(r => r.type === 'fail').length;
                
                if (failCount === 0) {
                    addResult(`🎯 TEST SUMMARY: ${passCount} tests passed, ${failCount} failed. Image loading fix appears to be working!`, 'pass');
                } else {
                    addResult(`⚠️ TEST SUMMARY: ${passCount} tests passed, ${failCount} failed. Some issues detected.`, 'warning');
                }
                
            } catch (error) {
                console.error('Test failed:', error);
                addResult(`❌ Test failed: ${error.message}`, 'fail');
            } finally {
                startBtn.disabled = false;
                startBtn.textContent = 'Start Comprehensive Test';
                updateProgress(totalSteps, 'Test completed');
            }
        }

        function validateImageLocally(dataURL) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve(true);
                img.onerror = () => resolve(false);
                img.src = dataURL;
            });
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            testResults = [];
            updateProgress(0, 'Ready to start testing...');
        }

        function clearConsole() {
            document.getElementById('consoleOutput').textContent = 'Console cleared...\n';
        }

        // Listen for messages from projector
        window.addEventListener('message', function(event) {
            console.log('Received message from projector:', event.data);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (projectorWindow && !projectorWindow.closed) {
                projectorWindow.close();
            }
        });

        console.log('Comprehensive test page loaded and ready');
    </script>
</body>
</html>
