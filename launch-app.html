<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bible Picture Story Display - Launcher</title>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #5cb85c;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --bg-color: #f8f9fa;
            --text-color: #333;
            --border-color: #dee2e6;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .launcher-container {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid var(--warning-color);
        }

        .status-card.error {
            border-left-color: var(--danger-color);
            background: #fff5f5;
        }

        .status-card.success {
            border-left-color: var(--secondary-color);
            background: #f0fff4;
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
        }

        .btn:hover {
            background-color: #357abd;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-secondary:hover {
            background-color: #449d44;
        }

        .btn-danger {
            background-color: var(--danger-color);
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .instructions {
            text-align: left;
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .instructions h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .instructions ol {
            margin-left: 1.5rem;
        }

        .instructions li {
            margin-bottom: 0.5rem;
        }

        .server-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .server-option {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .server-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .server-option h4 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .hidden {
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .protocol-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="launcher-container">
        <div class="logo">📖</div>
        <h1>Bible Picture Story Display</h1>
        <p class="subtitle">Professional dual-window presentation system</p>

        <div id="protocolCheck" class="status-card">
            <div class="loading"></div>
            <strong>Checking compatibility...</strong>
            <p>Detecting if the application can run in your current environment.</p>
        </div>

        <div id="fileProtocolWarning" class="protocol-warning hidden">
            <strong>⚠️ File Protocol Detected</strong>
            <p>You're running this from a file:// URL. The application requires a web server to function properly due to browser security restrictions.</p>
        </div>

        <div id="serverInstructions" class="instructions hidden">
            <h3>🚀 Quick Server Setup</h3>
            <p>Choose one of these options to run the application:</p>
            
            <div class="server-options">
                <div class="server-option">
                    <h4>Option 1: Batch File</h4>
                    <p>Double-click <code>start-server.bat</code> in the app folder</p>
                    <button class="btn btn-secondary" onclick="downloadBatchFile()">Download .bat</button>
                </div>
                
                <div class="server-option">
                    <h4>Option 2: PowerShell</h4>
                    <p>Right-click <code>start-server.ps1</code> → "Run with PowerShell"</p>
                    <button class="btn btn-secondary" onclick="downloadPSFile()">Download .ps1</button>
                </div>
                
                <div class="server-option">
                    <h4>Option 3: Python</h4>
                    <p>Open terminal in app folder, run:<br><code>python -m http.server 8000</code></p>
                </div>
                
                <div class="server-option">
                    <h4>Option 4: Node.js</h4>
                    <p>Install http-server:<br><code>npx http-server -p 8000</code></p>
                </div>
            </div>

            <ol>
                <li>Use any of the server options above</li>
                <li>Open your browser to <code>http://localhost:8000</code></li>
                <li>Click on "getting-started.html" to begin</li>
                <li>Follow the in-app instructions</li>
            </ol>
        </div>

        <div id="successMessage" class="status-card success hidden">
            <strong>✅ Ready to Launch!</strong>
            <p>Your environment supports the application. You can start using it right away!</p>
        </div>

        <div id="actionButtons" class="hidden">
            <a href="getting-started.html" class="btn">🏠 Getting Started</a>
            <a href="index.html" class="btn btn-secondary">🎮 Control Panel</a>
            <a href="test-functionality.html" class="btn">🧪 Test Features</a>
        </div>

        <div id="alternativeOptions" class="instructions hidden">
            <h3>🌐 Alternative Options</h3>
            <ul style="margin-left: 1.5rem;">
                <li><strong>VS Code:</strong> Install "Live Server" extension, right-click any HTML file → "Open with Live Server"</li>
                <li><strong>XAMPP/WAMP:</strong> Copy files to htdocs/www folder</li>
                <li><strong>Online Hosting:</strong> Upload to GitHub Pages, Netlify, or Vercel</li>
                <li><strong>Browser Extensions:</strong> Some browsers have local server extensions</li>
            </ul>
        </div>

        <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid var(--border-color); color: #666; font-size: 0.9rem;">
            <p>Need help? Check the README.md file for detailed instructions.</p>
        </div>
    </div>

    <script>
        function checkEnvironment() {
            const protocol = window.location.protocol;
            const isFileProtocol = protocol === 'file:';
            
            // Hide loading
            document.getElementById('protocolCheck').classList.add('hidden');
            
            if (isFileProtocol) {
                // Show file protocol warning and server instructions
                document.getElementById('fileProtocolWarning').classList.remove('hidden');
                document.getElementById('serverInstructions').classList.remove('hidden');
                document.getElementById('alternativeOptions').classList.remove('hidden');
            } else {
                // Show success message and action buttons
                document.getElementById('successMessage').classList.remove('hidden');
                document.getElementById('actionButtons').classList.remove('hidden');
                
                // Test if we can access other files
                testFileAccess();
            }
        }

        function testFileAccess() {
            // Try to fetch a file to make sure everything works
            fetch('getting-started.html')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Cannot access files');
                    }
                    console.log('File access test passed');
                })
                .catch(error => {
                    console.warn('File access test failed:', error);
                    // Show server instructions as backup
                    document.getElementById('successMessage').classList.add('hidden');
                    document.getElementById('actionButtons').classList.add('hidden');
                    document.getElementById('serverInstructions').classList.remove('hidden');
                    document.getElementById('alternativeOptions').classList.remove('hidden');
                });
        }

        function downloadBatchFile() {
            const batchContent = `@echo off
title Bible Picture Story Display - Local Server
echo Starting Bible Picture Story Display Server...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python found! Starting server...
    echo Server available at: http://localhost:8000
    echo Press Ctrl+C to stop the server
    echo.
    start http://localhost:8000
    python -m http.server 8000
    goto :end
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo Node.js found! Starting server...
    echo Server available at: http://localhost:8000
    echo Press Ctrl+C to stop the server
    echo.
    start http://localhost:8000
    npx http-server -p 8000
    goto :end
)

echo No compatible server found. Please install Python or Node.js.
echo Visit: https://www.python.org/downloads/ or https://nodejs.org/
pause

:end`;

            downloadFile('start-server.bat', batchContent);
        }

        function downloadPSFile() {
            const psContent = `# Bible Picture Story Display - PowerShell Server
Write-Host "Starting Bible Picture Story Display Server..." -ForegroundColor Green

try {
    python --version 2>&1 | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Python found! Starting server..." -ForegroundColor Green
        Write-Host "Server available at: http://localhost:8000" -ForegroundColor Yellow
        Start-Process "http://localhost:8000"
        python -m http.server 8000
        exit
    }
} catch {}

try {
    node --version 2>&1 | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Node.js found! Starting server..." -ForegroundColor Green
        Write-Host "Server available at: http://localhost:8000" -ForegroundColor Yellow
        Start-Process "http://localhost:8000"
        npx http-server -p 8000
        exit
    }
} catch {}

Write-Host "No compatible server found. Please install Python or Node.js." -ForegroundColor Red
Write-Host "Visit: https://www.python.org/downloads/ or https://nodejs.org/" -ForegroundColor Yellow
Read-Host "Press Enter to exit"`;

            downloadFile('start-server.ps1', psContent);
        }

        function downloadFile(filename, content) {
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
        }

        // Run check when page loads
        window.addEventListener('load', () => {
            setTimeout(checkEnvironment, 1000);
        });
    </script>
</body>
</html>
