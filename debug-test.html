<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Bible Picture Story Display</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        #fileInput { margin: 10px 0; }
        #dropZone {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            margin: 10px 0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        #dropZone.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .console-output {
            background-color: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Debug Test - Bible Picture Story Display</h1>
    
    <div class="test-section">
        <h2>Environment Tests</h2>
        <div id="environmentTests"></div>
        <button onclick="runEnvironmentTests()">Run Environment Tests</button>
    </div>

    <div class="test-section">
        <h2>File Upload Test</h2>
        <div id="dropZone">
            <p>Drag and drop an image here or click to select</p>
            <input type="file" id="fileInput" accept="image/*" multiple>
        </div>
        <div id="uploadResults"></div>
    </div>

    <div class="test-section">
        <h2>DOM Element Test</h2>
        <div id="domTests"></div>
        <button onclick="testDOMElements()">Test DOM Elements</button>
    </div>

    <div class="test-section">
        <h2>Local Storage Test</h2>
        <div id="storageTests"></div>
        <button onclick="testLocalStorage()">Test Local Storage</button>
    </div>

    <div class="test-section">
        <h2>Window Communication Test</h2>
        <div id="windowTests"></div>
        <button onclick="testWindowCommunication()">Test Window Communication</button>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <div id="consoleOutput" class="console-output">Console messages will appear here...</div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <div class="test-section">
        <h2>Quick Actions</h2>
        <button onclick="openControlPanel()">Open Control Panel</button>
        <button onclick="openProjector()">Open Projector</button>
        <button onclick="location.reload()">Reload Page</button>
    </div>

    <script>
        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌ ERROR' : type === 'warn' ? '⚠️ WARN' : '📝 LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function addResult(containerId, message, isPass) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${isPass ? 'pass' : 'fail'}`;
            div.textContent = (isPass ? '✅ ' : '❌ ') + message;
            container.appendChild(div);
        }

        function addInfo(containerId, message) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = 'test-result info';
            div.textContent = '📋 ' + message;
            container.appendChild(div);
        }

        function runEnvironmentTests() {
            const container = document.getElementById('environmentTests');
            container.innerHTML = '';
            
            // Test protocol
            const protocol = window.location.protocol;
            addResult('environmentTests', `Protocol: ${protocol}`, protocol === 'http:' || protocol === 'https:');
            
            // Test File API
            const hasFileAPI = !!(window.File && window.FileReader && window.FileList && window.Blob);
            addResult('environmentTests', 'File API Support', hasFileAPI);
            
            // Test Local Storage
            let hasLocalStorage = false;
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                hasLocalStorage = true;
            } catch (e) {}
            addResult('environmentTests', 'Local Storage Support', hasLocalStorage);
            
            // Test PostMessage
            const hasPostMessage = !!window.postMessage;
            addResult('environmentTests', 'PostMessage API Support', hasPostMessage);
            
            // Test ES6 features
            let hasES6 = false;
            try {
                eval('const test = () => `ES6 ${2021}`;');
                hasES6 = true;
            } catch (e) {}
            addResult('environmentTests', 'ES6 Support', hasES6);
            
            addInfo('environmentTests', `User Agent: ${navigator.userAgent}`);
        }

        function testDOMElements() {
            const container = document.getElementById('domTests');
            container.innerHTML = '';
            
            const elements = [
                'uploadArea', 'imageUpload', 'imageList', 'previewArea', 'textOverlay',
                'prevSlideBtn', 'nextSlideBtn', 'slideCounter', 'openProjectorBtn'
            ];
            
            elements.forEach(elementId => {
                const element = document.getElementById(elementId);
                addResult('domTests', `Element ${elementId}`, !!element);
            });
        }

        function testLocalStorage() {
            const container = document.getElementById('storageTests');
            container.innerHTML = '';
            
            try {
                const testData = { test: true, timestamp: Date.now() };
                localStorage.setItem('bibleStoryTest', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('bibleStoryTest'));
                localStorage.removeItem('bibleStoryTest');
                
                addResult('storageTests', 'Write to Local Storage', true);
                addResult('storageTests', 'Read from Local Storage', !!retrieved);
                addResult('storageTests', 'Data Integrity', retrieved.test === true);
            } catch (error) {
                addResult('storageTests', 'Local Storage Error: ' + error.message, false);
            }
        }

        function testWindowCommunication() {
            const container = document.getElementById('windowTests');
            container.innerHTML = '';
            
            try {
                const testWindow = window.open('about:blank', 'test', 'width=300,height=200');
                if (testWindow) {
                    addResult('windowTests', 'Open New Window', true);
                    
                    setTimeout(() => {
                        try {
                            testWindow.postMessage({ type: 'TEST', data: 'Hello' }, '*');
                            addResult('windowTests', 'Send PostMessage', true);
                            testWindow.close();
                        } catch (error) {
                            addResult('windowTests', 'PostMessage Error: ' + error.message, false);
                            testWindow.close();
                        }
                    }, 1000);
                } else {
                    addResult('windowTests', 'Window Opening (Pop-up blocked?)', false);
                }
            } catch (error) {
                addResult('windowTests', 'Window Communication Error: ' + error.message, false);
            }
        }

        function clearConsole() {
            document.getElementById('consoleOutput').textContent = 'Console cleared...\n';
        }

        function openControlPanel() {
            window.open('index.html', '_blank');
        }

        function openProjector() {
            window.open('projector.html', '_blank');
        }

        // File upload test
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            const container = document.getElementById('uploadResults');
            container.innerHTML = '';
            
            addInfo('uploadResults', `Selected ${files.length} file(s)`);
            
            files.forEach((file, index) => {
                addInfo('uploadResults', `File ${index + 1}: ${file.name} (${file.type}, ${(file.size / 1024).toFixed(1)} KB)`);
                
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        addResult('uploadResults', `Successfully read: ${file.name}`, true);
                    };
                    reader.onerror = function() {
                        addResult('uploadResults', `Failed to read: ${file.name}`, false);
                    };
                    reader.readAsDataURL(file);
                } else {
                    addResult('uploadResults', `Not an image: ${file.name}`, false);
                }
            });
        });

        // Drag and drop test
        const dropZone = document.getElementById('dropZone');
        
        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });
        
        dropZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            dropZone.classList.remove('dragover');
        });
        
        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files);
            const container = document.getElementById('uploadResults');
            container.innerHTML = '';
            
            addInfo('uploadResults', `Dropped ${files.length} file(s)`);
            console.log('Files dropped:', files);
        });
        
        dropZone.addEventListener('click', function() {
            document.getElementById('fileInput').click();
        });

        // Auto-run environment tests on load
        window.addEventListener('load', function() {
            console.log('Debug test page loaded');
            setTimeout(runEnvironmentTests, 500);
        });
    </script>
</body>
</html>
