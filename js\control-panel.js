// Bible Picture Story Display - Control Panel JavaScript
class BibleStoryControlPanel {
    constructor() {
        this.stories = [];
        this.currentStoryIndex = -1;
        this.currentSlideIndex = 0;
        this.projectorWindow = null;
        this.isConnected = false;
        this.autoSaveInterval = null;

        try {
            this.initializeElements();
            this.bindEvents();
            this.loadFromLocalStorage();
            this.startAutoSave();
            this.setupKeyboardShortcuts();
            console.log('All components initialized successfully');
        } catch (error) {
            console.error('Error in constructor:', error);
            throw error;
        }
    }

    initializeElements() {
        console.log('Initializing DOM elements...');

        // Main elements
        this.uploadArea = document.getElementById('uploadArea');
        this.imageUpload = document.getElementById('imageUpload');
        this.imageList = document.getElementById('imageList');
        this.previewArea = document.getElementById('previewArea');
        this.textOverlay = document.getElementById('textOverlay');

        // Check if critical elements exist
        const criticalElements = [
            'uploadArea', 'imageUpload', 'imageList', 'previewArea', 'textOverlay'
        ];

        for (const elementName of criticalElements) {
            if (!this[elementName]) {
                throw new Error(`Critical element not found: ${elementName}`);
            }
        }
        
        // Navigation elements
        this.prevSlideBtn = document.getElementById('prevSlideBtn');
        this.nextSlideBtn = document.getElementById('nextSlideBtn');
        this.slideCounter = document.getElementById('slideCounter');
        this.jumpToSlide = document.getElementById('jumpToSlide');
        this.jumpBtn = document.getElementById('jumpBtn');
        
        // Control elements
        this.openProjectorBtn = document.getElementById('openProjectorBtn');
        this.exportStoryBtn = document.getElementById('exportStoryBtn');
        this.importStoryBtn = document.getElementById('importStoryBtn');
        this.importFileInput = document.getElementById('importFileInput');
        
        // Text formatting elements
        this.fontSize = document.getElementById('fontSize');
        this.fontSizeValue = document.getElementById('fontSizeValue');
        this.textColor = document.getElementById('textColor');
        this.boldBtn = document.getElementById('boldBtn');
        this.italicBtn = document.getElementById('italicBtn');
        this.underlineBtn = document.getElementById('underlineBtn');
        this.alignLeftBtn = document.getElementById('alignLeftBtn');
        this.alignCenterBtn = document.getElementById('alignCenterBtn');
        this.alignRightBtn = document.getElementById('alignRightBtn');
        
        // Animation elements
        this.zoomInBtn = document.getElementById('zoomInBtn');
        this.zoomOutBtn = document.getElementById('zoomOutBtn');
        this.resetZoomBtn = document.getElementById('resetZoomBtn');
        this.panUpBtn = document.getElementById('panUpBtn');
        this.panDownBtn = document.getElementById('panDownBtn');
        this.panLeftBtn = document.getElementById('panLeftBtn');
        this.panRightBtn = document.getElementById('panRightBtn');
        this.panCenterBtn = document.getElementById('panCenterBtn');
        this.animationSpeed = document.getElementById('animationSpeed');
        this.speedValue = document.getElementById('speedValue');
        this.animationDuration = document.getElementById('animationDuration');
        this.durationValue = document.getElementById('durationValue');
        
        // Status elements
        this.connectionStatus = document.getElementById('connectionStatus');
        this.lastSaved = document.getElementById('lastSaved');
    }

    bindEvents() {
        console.log('Binding events...');

        // Upload events
        console.log('Setting up upload events...');
        this.uploadArea.addEventListener('click', () => {
            console.log('Upload area clicked');
            this.imageUpload.click();
        });
        this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        this.imageUpload.addEventListener('change', this.handleFileSelect.bind(this));
        
        // Navigation events
        this.prevSlideBtn.addEventListener('click', () => this.previousSlide());
        this.nextSlideBtn.addEventListener('click', () => this.nextSlide());
        this.jumpBtn.addEventListener('click', () => this.jumpToSlideNumber());
        this.jumpToSlide.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.jumpToSlideNumber();
        });
        
        // Control events
        this.openProjectorBtn.addEventListener('click', () => this.openProjectorWindow());
        this.exportStoryBtn.addEventListener('click', () => this.exportStory());
        this.importStoryBtn.addEventListener('click', () => this.importFileInput.click());
        this.importFileInput.addEventListener('change', this.handleImport.bind(this));
        
        // Text overlay events
        this.textOverlay.addEventListener('input', () => this.updateTextOverlay());
        this.fontSize.addEventListener('input', () => this.updateFontSize());
        this.textColor.addEventListener('change', () => this.updateTextOverlay());
        
        // Text formatting events
        this.boldBtn.addEventListener('click', () => this.toggleFormat('bold'));
        this.italicBtn.addEventListener('click', () => this.toggleFormat('italic'));
        this.underlineBtn.addEventListener('click', () => this.toggleFormat('underline'));
        this.alignLeftBtn.addEventListener('click', () => this.setAlignment('left'));
        this.alignCenterBtn.addEventListener('click', () => this.setAlignment('center'));
        this.alignRightBtn.addEventListener('click', () => this.setAlignment('right'));
        
        // Animation events
        this.zoomInBtn.addEventListener('click', () => this.sendAnimation('zoom-in'));
        this.zoomOutBtn.addEventListener('click', () => this.sendAnimation('zoom-out'));
        this.resetZoomBtn.addEventListener('click', () => this.sendAnimation('reset'));
        this.panUpBtn.addEventListener('click', () => this.sendAnimation('pan-up'));
        this.panDownBtn.addEventListener('click', () => this.sendAnimation('pan-down'));
        this.panLeftBtn.addEventListener('click', () => this.sendAnimation('pan-left'));
        this.panRightBtn.addEventListener('click', () => this.sendAnimation('pan-right'));
        this.panCenterBtn.addEventListener('click', () => this.sendAnimation('pan-center'));
        
        this.animationSpeed.addEventListener('input', () => this.updateSpeedValue());
        this.animationDuration.addEventListener('input', () => this.updateDurationValue());
        
        // Window communication
        window.addEventListener('message', this.handleMessage.bind(this));
        window.addEventListener('beforeunload', () => this.saveToLocalStorage());
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Prevent default for our shortcuts
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                switch(e.key) {
                    case 'ArrowLeft':
                        e.preventDefault();
                        this.previousSlide();
                        break;
                    case 'ArrowRight':
                    case ' ':
                        e.preventDefault();
                        this.nextSlide();
                        break;
                    case 'f':
                    case 'F':
                        if (!e.ctrlKey) {
                            e.preventDefault();
                            this.toggleFullscreen();
                        }
                        break;
                    case '?':
                        e.preventDefault();
                        this.showHelp();
                        break;
                }
            }
            
            // Ctrl shortcuts
            if (e.ctrlKey) {
                switch(e.key) {
                    case 's':
                        e.preventDefault();
                        this.saveToLocalStorage();
                        break;
                    case 'o':
                        e.preventDefault();
                        this.openProjectorWindow();
                        break;
                }
            }
        });
    }

    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        console.log('Drop event triggered');
        e.preventDefault();
        e.stopPropagation();
        this.uploadArea.classList.remove('dragover');
        const files = Array.from(e.dataTransfer.files);
        console.log('Dropped files:', files);
        this.processFiles(files);
    }

    handleFileSelect(e) {
        console.log('File select event triggered');
        const files = Array.from(e.target.files);
        console.log('Selected files:', files);
        this.processFiles(files);
    }

    processFiles(files) {
        console.log('Processing files:', files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));
        console.log('Image files found:', imageFiles);

        if (imageFiles.length === 0) {
            console.warn('No image files found');
            alert('Please select image files (JPG, PNG, GIF)');
            return;
        }

        imageFiles.forEach(file => {
            console.log('Processing file:', file.name, file.type, file.size);
            const reader = new FileReader();
            reader.onload = (e) => {
                const result = e.target.result;
                console.log('File read complete:', file.name);
                console.log('Data URL length:', result.length);
                console.log('Data URL preview:', result.substring(0, 100) + '...');

                const imageData = {
                    id: Date.now() + Math.random(),
                    name: file.name,
                    size: this.formatFileSize(file.size),
                    src: result,
                    text: '',
                    textStyle: {
                        fontSize: 24,
                        color: '#ffffff',
                        bold: false,
                        italic: false,
                        underline: false,
                        alignment: 'center'
                    }
                };

                console.log('Created image data:', imageData);
                this.stories.push(imageData);
                this.renderImageList();
                this.updateNavigation();

                // Select the first image if none selected
                if (this.currentStoryIndex === -1) {
                    this.selectSlide(0);
                }
            };
            reader.onerror = (e) => {
                console.error('Error reading file:', file.name, e);
                alert('Error reading file: ' + file.name);
            };
            reader.readAsDataURL(file);
        });
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    renderImageList() {
        this.imageList.innerHTML = '';
        
        this.stories.forEach((story, index) => {
            const item = document.createElement('div');
            item.className = `image-item ${index === this.currentStoryIndex ? 'active' : ''}`;
            item.draggable = true;
            item.dataset.index = index;
            
            item.innerHTML = `
                <img src="${story.src}" alt="${story.name}" class="image-thumbnail">
                <div class="image-info">
                    <div class="image-name">${story.name}</div>
                    <div class="image-size">${story.size}</div>
                </div>
                <div class="image-actions">
                    <button onclick="controlPanel.moveSlide(${index}, -1)" title="Move Up">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button onclick="controlPanel.moveSlide(${index}, 1)" title="Move Down">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button onclick="controlPanel.deleteSlide(${index})" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            
            item.addEventListener('click', () => this.selectSlide(index));
            item.addEventListener('dragstart', this.handleDragStart.bind(this));
            item.addEventListener('dragover', this.handleDragOver.bind(this));
            item.addEventListener('drop', this.handleDropReorder.bind(this));
            
            this.imageList.appendChild(item);
        });
    }

    selectSlide(index) {
        if (index < 0 || index >= this.stories.length) return;
        
        this.currentStoryIndex = index;
        this.renderImageList();
        this.updatePreview();
        this.updateNavigation();
        this.loadSlideText();
        this.sendSlideUpdate();
    }

    updatePreview() {
        if (this.currentStoryIndex === -1 || !this.stories[this.currentStoryIndex]) {
            this.previewArea.innerHTML = `
                <div class="preview-placeholder">
                    <i class="fas fa-image"></i>
                    <p>No image selected</p>
                </div>
            `;
            return;
        }
        
        const story = this.stories[this.currentStoryIndex];
        this.previewArea.innerHTML = `
            <img src="${story.src}" alt="${story.name}" class="preview-image">
            <div class="preview-text-overlay" style="
                position: absolute;
                bottom: 10%;
                left: 50%;
                transform: translateX(-50%);
                color: ${story.textStyle.color};
                font-size: ${story.textStyle.fontSize}px;
                font-weight: ${story.textStyle.bold ? 'bold' : 'normal'};
                font-style: ${story.textStyle.italic ? 'italic' : 'normal'};
                text-decoration: ${story.textStyle.underline ? 'underline' : 'none'};
                text-align: ${story.textStyle.alignment};
                background: rgba(0, 0, 0, 0.7);
                padding: 0.5rem 1rem;
                border-radius: 4px;
                max-width: 90%;
                ${story.text ? '' : 'display: none;'}
            ">${story.text}</div>
        `;
    }

    // Continue with more methods...
    updateNavigation() {
        const total = this.stories.length;
        const current = this.currentStoryIndex + 1;
        
        this.slideCounter.textContent = `${current} / ${total}`;
        this.prevSlideBtn.disabled = this.currentStoryIndex <= 0;
        this.nextSlideBtn.disabled = this.currentStoryIndex >= total - 1;
        this.jumpToSlide.max = total;
        this.jumpToSlide.value = current;
    }

    previousSlide() {
        if (this.currentStoryIndex > 0) {
            this.selectSlide(this.currentStoryIndex - 1);
        }
    }

    nextSlide() {
        if (this.currentStoryIndex < this.stories.length - 1) {
            this.selectSlide(this.currentStoryIndex + 1);
        }
    }

    jumpToSlideNumber() {
        const slideNumber = parseInt(this.jumpToSlide.value);
        if (slideNumber >= 1 && slideNumber <= this.stories.length) {
            this.selectSlide(slideNumber - 1);
        }
    }

    loadSlideText() {
        if (this.currentStoryIndex >= 0 && this.stories[this.currentStoryIndex]) {
            const story = this.stories[this.currentStoryIndex];
            this.textOverlay.value = story.text || '';
            this.fontSize.value = story.textStyle.fontSize;
            this.fontSizeValue.textContent = story.textStyle.fontSize + 'px';
            this.textColor.value = story.textStyle.color;
            
            // Update format buttons
            this.boldBtn.classList.toggle('active', story.textStyle.bold);
            this.italicBtn.classList.toggle('active', story.textStyle.italic);
            this.underlineBtn.classList.toggle('active', story.textStyle.underline);
            
            // Update alignment buttons
            document.querySelectorAll('.format-btn[id*="align"]').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`align${story.textStyle.alignment.charAt(0).toUpperCase() + story.textStyle.alignment.slice(1)}Btn`).classList.add('active');
        }
    }

    updateTextOverlay() {
        if (this.currentStoryIndex >= 0 && this.stories[this.currentStoryIndex]) {
            this.stories[this.currentStoryIndex].text = this.textOverlay.value;
            this.stories[this.currentStoryIndex].textStyle.color = this.textColor.value;
            this.updatePreview();
            this.sendTextUpdate();
        }
    }

    updateFontSize() {
        const size = parseInt(this.fontSize.value);
        this.fontSizeValue.textContent = size + 'px';
        
        if (this.currentStoryIndex >= 0 && this.stories[this.currentStoryIndex]) {
            this.stories[this.currentStoryIndex].textStyle.fontSize = size;
            this.updatePreview();
            this.sendTextUpdate();
        }
    }

    toggleFormat(format) {
        if (this.currentStoryIndex >= 0 && this.stories[this.currentStoryIndex]) {
            const story = this.stories[this.currentStoryIndex];
            story.textStyle[format] = !story.textStyle[format];
            
            document.getElementById(`${format}Btn`).classList.toggle('active', story.textStyle[format]);
            this.updatePreview();
            this.sendTextUpdate();
        }
    }

    setAlignment(alignment) {
        if (this.currentStoryIndex >= 0 && this.stories[this.currentStoryIndex]) {
            this.stories[this.currentStoryIndex].textStyle.alignment = alignment;
            
            // Update button states
            document.querySelectorAll('.format-btn[id*="align"]').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`align${alignment.charAt(0).toUpperCase() + alignment.slice(1)}Btn`).classList.add('active');
            
            this.updatePreview();
            this.sendTextUpdate();
        }
    }

    updateSpeedValue() {
        this.speedValue.textContent = parseFloat(this.animationSpeed.value).toFixed(1) + 'x';
    }

    updateDurationValue() {
        this.durationValue.textContent = (parseInt(this.animationDuration.value) / 1000).toFixed(1) + 's';
    }

    openProjectorWindow() {
        if (this.projectorWindow && !this.projectorWindow.closed) {
            this.projectorWindow.focus();
            return;
        }

        this.projectorWindow = window.open(
            'projector.html',
            'projector',
            'fullscreen=yes,scrollbars=no,resizable=yes,status=no,toolbar=no,menubar=no'
        );

        if (this.projectorWindow) {
            this.projectorWindow.addEventListener('load', () => {
                this.isConnected = true;
                this.updateConnectionStatus();
                this.sendInitialData();
            });

            // Check if window is closed
            const checkClosed = setInterval(() => {
                if (this.projectorWindow.closed) {
                    this.isConnected = false;
                    this.updateConnectionStatus();
                    clearInterval(checkClosed);
                }
            }, 1000);
        }
    }

    updateConnectionStatus() {
        const statusIcon = this.connectionStatus.querySelector('i');
        const statusText = this.connectionStatus.querySelector('span');

        if (this.isConnected) {
            statusIcon.className = 'fas fa-circle status-connected';
            statusText.textContent = 'Projector Connected';
        } else {
            statusIcon.className = 'fas fa-circle status-disconnected';
            statusText.textContent = 'Projector Disconnected';
        }
    }

    sendMessage(type, data) {
        if (this.projectorWindow && !this.projectorWindow.closed) {
            console.log('Sending message to projector:', type, data);
            this.projectorWindow.postMessage({ type, data }, '*');
        } else {
            console.warn('Cannot send message - projector window not available');
        }
    }

    sendInitialData() {
        this.sendMessage('INIT', {
            stories: this.stories,
            currentIndex: this.currentStoryIndex,
            animationSettings: {
                speed: parseFloat(this.animationSpeed.value),
                duration: parseInt(this.animationDuration.value)
            }
        });
    }

    sendSlideUpdate() {
        if (this.currentStoryIndex >= 0 && this.stories[this.currentStoryIndex]) {
            this.sendMessage('SLIDE_CHANGE', {
                slide: this.stories[this.currentStoryIndex],
                index: this.currentStoryIndex
            });
        }
    }

    sendTextUpdate() {
        if (this.currentStoryIndex >= 0 && this.stories[this.currentStoryIndex]) {
            this.sendMessage('TEXT_UPDATE', {
                text: this.stories[this.currentStoryIndex].text,
                style: this.stories[this.currentStoryIndex].textStyle
            });
        }
    }

    sendAnimation(type) {
        this.sendMessage('ANIMATION', {
            type: type,
            speed: parseFloat(this.animationSpeed.value),
            duration: parseInt(this.animationDuration.value)
        });
    }

    handleMessage(event) {
        if (event.source === this.projectorWindow) {
            const { type, data } = event.data;

            switch (type) {
                case 'READY':
                    this.isConnected = true;
                    this.updateConnectionStatus();
                    this.sendInitialData();
                    break;
                case 'DISCONNECTED':
                    this.isConnected = false;
                    this.updateConnectionStatus();
                    break;
            }
        }
    }

    moveSlide(index, direction) {
        if (direction === -1 && index > 0) {
            // Move up
            [this.stories[index], this.stories[index - 1]] = [this.stories[index - 1], this.stories[index]];
            if (this.currentStoryIndex === index) this.currentStoryIndex--;
            else if (this.currentStoryIndex === index - 1) this.currentStoryIndex++;
        } else if (direction === 1 && index < this.stories.length - 1) {
            // Move down
            [this.stories[index], this.stories[index + 1]] = [this.stories[index + 1], this.stories[index]];
            if (this.currentStoryIndex === index) this.currentStoryIndex++;
            else if (this.currentStoryIndex === index + 1) this.currentStoryIndex--;
        }

        this.renderImageList();
        this.updateNavigation();
        this.sendInitialData(); // Update projector with new order
    }

    deleteSlide(index) {
        if (confirm('Are you sure you want to delete this slide?')) {
            this.stories.splice(index, 1);

            if (this.currentStoryIndex === index) {
                this.currentStoryIndex = Math.min(this.currentStoryIndex, this.stories.length - 1);
            } else if (this.currentStoryIndex > index) {
                this.currentStoryIndex--;
            }

            this.renderImageList();
            this.updateNavigation();
            this.updatePreview();

            if (this.stories.length === 0) {
                this.currentStoryIndex = -1;
            }

            this.sendInitialData();
        }
    }

    handleDragStart(e) {
        e.dataTransfer.setData('text/plain', e.target.dataset.index);
        e.target.classList.add('dragging');
    }

    handleDropReorder(e) {
        e.preventDefault();
        const draggedIndex = parseInt(e.dataTransfer.getData('text/plain'));
        const targetIndex = parseInt(e.target.closest('.image-item').dataset.index);

        if (draggedIndex !== targetIndex) {
            const draggedItem = this.stories.splice(draggedIndex, 1)[0];
            this.stories.splice(targetIndex, 0, draggedItem);

            // Update current index
            if (this.currentStoryIndex === draggedIndex) {
                this.currentStoryIndex = targetIndex;
            } else if (draggedIndex < this.currentStoryIndex && targetIndex >= this.currentStoryIndex) {
                this.currentStoryIndex--;
            } else if (draggedIndex > this.currentStoryIndex && targetIndex <= this.currentStoryIndex) {
                this.currentStoryIndex++;
            }

            this.renderImageList();
            this.updateNavigation();
            this.sendInitialData();
        }

        document.querySelectorAll('.image-item').forEach(item => item.classList.remove('dragging'));
    }

    exportStory() {
        const storyData = {
            version: '1.0',
            title: 'Bible Picture Story',
            created: new Date().toISOString(),
            slides: this.stories
        };

        const dataStr = JSON.stringify(storyData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `bible-story-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        URL.revokeObjectURL(link.href);
    }

    handleImport(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const storyData = JSON.parse(e.target.result);

                if (storyData.slides && Array.isArray(storyData.slides)) {
                    if (confirm('This will replace your current story. Continue?')) {
                        this.stories = storyData.slides;
                        this.currentStoryIndex = this.stories.length > 0 ? 0 : -1;
                        this.renderImageList();
                        this.updateNavigation();
                        this.updatePreview();
                        this.loadSlideText();
                        this.sendInitialData();
                    }
                } else {
                    alert('Invalid story file format.');
                }
            } catch (error) {
                alert('Error reading story file: ' + error.message);
            }
        };
        reader.readAsText(file);

        // Reset input
        e.target.value = '';
    }

    saveToLocalStorage() {
        const data = {
            stories: this.stories,
            currentIndex: this.currentStoryIndex,
            lastSaved: new Date().toISOString()
        };

        localStorage.setItem('bibleStoryData', JSON.stringify(data));
        this.lastSaved.textContent = `Last saved: ${new Date().toLocaleTimeString()}`;
    }

    loadFromLocalStorage() {
        const saved = localStorage.getItem('bibleStoryData');
        if (saved) {
            try {
                const data = JSON.parse(saved);
                this.stories = data.stories || [];
                this.currentStoryIndex = data.currentIndex || -1;

                if (this.stories.length > 0) {
                    this.renderImageList();
                    this.updateNavigation();
                    this.updatePreview();
                    this.loadSlideText();
                }

                if (data.lastSaved) {
                    const lastSavedDate = new Date(data.lastSaved);
                    this.lastSaved.textContent = `Last saved: ${lastSavedDate.toLocaleString()}`;
                }
            } catch (error) {
                console.error('Error loading saved data:', error);
            }
        }
    }

    startAutoSave() {
        this.autoSaveInterval = setInterval(() => {
            this.saveToLocalStorage();
        }, 30000); // Auto-save every 30 seconds
    }

    toggleFullscreen() {
        if (this.projectorWindow && !this.projectorWindow.closed) {
            this.projectorWindow.focus();
            this.sendMessage('TOGGLE_FULLSCREEN', {});
        }
    }

    showHelp() {
        const modal = document.getElementById('helpModal');
        modal.style.display = 'block';

        const closeBtn = modal.querySelector('.close');
        closeBtn.onclick = () => modal.style.display = 'none';

        window.onclick = (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        };
    }
}

// Initialize the control panel when the page loads
let controlPanel;
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('Initializing Bible Story Control Panel...');
        controlPanel = new BibleStoryControlPanel();
        console.log('Control Panel initialized successfully!');
    } catch (error) {
        console.error('Error initializing control panel:', error);
        alert('Error initializing application: ' + error.message);
    }
});
