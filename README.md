# Bible Picture Story Display

A comprehensive web-based application for creating and presenting Bible picture stories with dual-window support for control and projection.

## Features

### 🎮 Control Panel (Dark Mode)
- **Story Management**: Create, edit, and organize picture story presentations
- **Image Upload**: Drag-and-drop or browse to upload images (JPG, PNG, GIF)
- **Slide Ordering**: Drag-and-drop reordering of slides with visual feedback
- **Text Overlay Editor**: WYSIWYG text editor with formatting options
  - Font size adjustment (12-72px)
  - Color picker for text color
  - Bold, italic, underline formatting
  - Text alignment (left, center, right)
- **Real-time Animation Controls**:
  - Zoom in/out with adjustable scale
  - Pan controls (up, down, left, right, center)
  - Adjustable animation speed (0.5x - 3.0x)
  - Configurable duration (0.5s - 5.0s)
- **Navigation Controls**: Previous/next slide, jump to specific slide
- **Export/Import**: Save and load story configurations in JSON format
- **Auto-save**: Automatic saving every 30 seconds to prevent data loss

### 📽️ Projector Display
- **Full-screen Optimized**: Clean, distraction-free interface for audience
- **Smooth Transitions**: Professional slide transitions with fade effects
- **Real-time Text Overlay**: Live text rendering with applied formatting
- **Animation Support**: Real-time zoom and pan effects controlled from panel
- **Connection Status**: Visual indicator of control panel connection
- **Keyboard Navigation**: Arrow keys and spacebar for slide control

### 🔗 Window Communication
- **PostMessage API**: Secure communication between control and projector windows
- **Real-time Sync**: Instant updates of slides, text, and animations
- **Connection Monitoring**: Automatic detection of window state changes
- **Cross-window Control**: Full control from panel to projector display

## Installation & Setup

1. **Download/Clone** the application files to your local machine
2. **Open** `index.html` in a modern web browser (Chrome, Firefox, Safari, Edge)
3. **No server required** - runs entirely in the browser using local files

## Usage Guide

### Getting Started
1. Open `index.html` in your web browser
2. Click "Open Projector Display" to launch the projection window
3. Position the projector window on your extended display/projector
4. Use the control panel to manage your presentation

### Creating a Story
1. **Upload Images**: 
   - Drag and drop images into the upload area, or
   - Click the upload area to browse and select files
2. **Arrange Slides**: 
   - Drag slides up/down to reorder
   - Use arrow buttons for fine positioning
3. **Add Text Overlays**:
   - Select a slide from the image list
   - Type text in the text overlay field
   - Adjust formatting using the controls
4. **Preview**: View your changes in the preview area

### During Presentation
1. **Navigate Slides**:
   - Use Previous/Next buttons
   - Jump to specific slide numbers
   - Keyboard shortcuts (← → Space)
2. **Control Animations**:
   - Zoom in/out for emphasis
   - Pan to focus on specific areas
   - Adjust speed and duration as needed
3. **Text Display**:
   - Text overlays appear automatically
   - Formatting is applied in real-time

### Keyboard Shortcuts

#### Control Panel
- `←` / `→` : Navigate slides
- `Space` : Next slide
- `F` : Toggle projector fullscreen
- `Ctrl + S` : Save story
- `Ctrl + O` : Open projector window
- `?` : Show help dialog

#### Projector Window
- `←` / `→` : Navigate slides
- `Space` : Next slide
- `Esc` : Exit fullscreen
- `F11` : Toggle fullscreen

## File Management

### Export Stories
1. Click "Export" button in the control panel
2. Choose save location for the JSON file
3. File includes all images (as base64) and settings

### Import Stories
1. Click "Import" button in the control panel
2. Select a previously exported JSON file
3. Confirm to replace current story

### Auto-save
- Stories are automatically saved to browser local storage
- Data persists between browser sessions
- Manual save available with Ctrl+S

## Technical Specifications

### Browser Requirements
- **Modern Browser**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **JavaScript**: ES6+ support required
- **Local Storage**: For auto-save functionality
- **File API**: For image upload and export/import

### File Formats
- **Images**: JPG, PNG, GIF (common web formats)
- **Export**: JSON format with embedded base64 images
- **Text**: UTF-8 encoding for international characters

### Performance
- **Image Size**: Automatically optimized for display
- **Memory Usage**: Efficient base64 encoding
- **Animation**: Hardware-accelerated CSS transforms
- **Responsive**: Adapts to different screen resolutions

## Troubleshooting

### Common Issues

**Projector window won't open:**
- Check if pop-up blocker is enabled
- Ensure JavaScript is enabled
- Try refreshing the control panel

**Images not displaying:**
- Verify image file format (JPG, PNG, GIF)
- Check file size (very large files may cause issues)
- Ensure stable internet connection for external images

**Animation lag:**
- Reduce animation duration
- Lower animation speed
- Close other browser tabs/applications

**Connection lost:**
- Refresh both windows
- Re-open projector window from control panel
- Check browser console for error messages

### Browser Compatibility
- **Chrome**: Full support, recommended
- **Firefox**: Full support
- **Safari**: Full support (macOS/iOS)
- **Edge**: Full support (Chromium-based)
- **Internet Explorer**: Not supported

## Development

### File Structure
```
Bible_picture_story_display/
├── index.html              # Control panel interface
├── projector.html          # Projector display interface
├── css/
│   ├── control-panel.css   # Control panel styles
│   └── projector.css       # Projector display styles
├── js/
│   ├── control-panel.js    # Control panel logic
│   └── projector.js        # Projector display logic
└── README.md               # This documentation
```

### Customization
- **Themes**: Modify CSS variables in control-panel.css
- **Animations**: Adjust transition properties in projector.css
- **Features**: Extend JavaScript classes for additional functionality

## License

This project is open source and available under the MIT License.

## Support

For issues, questions, or feature requests, please refer to the troubleshooting section above or check the browser console for detailed error messages.

---

**Version**: 1.0  
**Last Updated**: June 2025  
**Compatibility**: Modern web browsers with ES6+ support
