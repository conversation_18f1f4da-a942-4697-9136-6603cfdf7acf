# 🚀 Bible Picture Story Display - Setup Guide

## Why Do I Need a Server?

The Bible Picture Story Display application requires a local web server to function properly due to modern browser security restrictions. When you open HTML files directly (file:// URLs), browsers block many features for security reasons, including:

- Loading other files (images, scripts, stylesheets)
- Window communication between control panel and projector
- Local storage and file operations
- Import/export functionality

## 📋 Quick Setup Instructions

### 🎯 Easiest Method: Use the Launcher

1. **Open** `launch-app.html` by double-clicking it
2. **Follow** the automatic setup instructions
3. **Download** the appropriate server script if needed
4. **Run** the server and enjoy!

### 🖥️ Windows Users: Batch File Method

1. **Double-click** `start-server.bat`
2. **Wait** for the server to start (may take a few seconds)
3. **<PERSON><PERSON><PERSON> opens automatically** to the application
4. **Keep the command window open** while using the app

### 💻 Windows Users: PowerShell Method

1. **Right-click** `start-server.ps1`
2. **Select** "Run with PowerShell"
3. **Allow** script execution if prompted
4. **<PERSON>rowser opens automatically** to the application

### 🐍 Python Users (All Platforms)

1. **Open terminal/command prompt** in the app folder
2. **Run**: `python -m http.server 8000`
3. **Open browser** to `http://localhost:8000`
4. **Click** on "getting-started.html"

### 🟢 Node.js Users (All Platforms)

1. **Open terminal/command prompt** in the app folder
2. **Run**: `npx http-server -p 8000`
3. **Browser opens automatically** or go to `http://localhost:8000`

## 🔧 Installation Requirements

You need **ONE** of the following installed on your computer:

### Option 1: Python (Recommended)
- **Download**: https://www.python.org/downloads/
- **Install**: Make sure to check "Add Python to PATH"
- **Verify**: Open command prompt, type `python --version`

### Option 2: Node.js
- **Download**: https://nodejs.org/
- **Install**: Use default settings
- **Verify**: Open command prompt, type `node --version`

### Option 3: Alternative Servers
- **XAMPP**: https://www.apachefriends.org/
- **WAMP**: http://www.wampserver.com/
- **VS Code Live Server**: Install extension in VS Code
- **Any other local web server**

## 🎮 Using the Application

Once your server is running:

1. **Navigate** to `http://localhost:8000` (or the port shown)
2. **Click** "getting-started.html" for instructions
3. **Start** with the sample story or upload your own images
4. **Open** the projector display on your second screen
5. **Present** your Bible story!

## 📁 File Structure

```
Bible_picture_story_display/
├── launch-app.html          # 🚀 Start here - Automatic launcher
├── start-server.bat         # 🖥️ Windows batch file
├── start-server.ps1         # 💻 PowerShell script
├── getting-started.html     # 📖 User guide and instructions
├── index.html              # 🎮 Control panel interface
├── projector.html          # 📽️ Projector display
├── test-functionality.html # 🧪 Feature testing tool
├── sample-story.json       # 📋 Sample David & Goliath story
├── README.md               # 📚 Complete documentation
├── css/                    # 🎨 Stylesheets
│   ├── control-panel.css
│   └── projector.css
└── js/                     # ⚙️ JavaScript functionality
    ├── control-panel.js
    └── projector.js
```

## ❓ Troubleshooting

### "Nothing happens when I click buttons"
- **Problem**: You're opening files directly (file:// URL)
- **Solution**: Use one of the server methods above

### "Pop-up blocked" or "Projector won't open"
- **Check**: Browser pop-up blocker settings
- **Allow**: Pop-ups for localhost
- **Try**: Different browser (Chrome recommended)

### "Server won't start"
- **Check**: Python or Node.js is installed
- **Verify**: Port 8000 isn't already in use
- **Try**: Different port number (edit the scripts)

### "Images won't upload"
- **Ensure**: You're using a web server (not file://)
- **Check**: Image format (JPG, PNG, GIF only)
- **Verify**: File size isn't too large

## 🎯 Quick Test

1. **Start** your server using any method above
2. **Open** `test-functionality.html`
3. **Click** "Run All Tests"
4. **Verify** all tests pass (green checkmarks)

## 🆘 Still Need Help?

1. **Check** the browser console (F12) for error messages
2. **Try** a different browser (Chrome, Firefox, Edge)
3. **Verify** your server is actually running
4. **Read** the complete README.md for detailed information
5. **Test** with the sample story first before using your own images

## 🎉 Success!

Once everything is working:
- ✅ Control panel loads with dark theme
- ✅ You can upload and arrange images
- ✅ Text overlay editor works
- ✅ Projector window opens and communicates
- ✅ Animations and navigation work smoothly
- ✅ Export/import functions properly

**Enjoy creating amazing Bible picture story presentations!** 📖✨
