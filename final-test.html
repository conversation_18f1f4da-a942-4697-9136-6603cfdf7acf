<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Test - Bible Picture Story Display</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        button:hover { 
            background-color: #0056b3; 
            transform: translateY(-2px);
        }
        .upload-test {
            border: 2px dashed #007bff;
            padding: 40px;
            text-align: center;
            margin: 15px 0;
            border-radius: 8px;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-test:hover, .upload-test.dragover {
            background-color: #e3f2fd;
            border-color: #0056b3;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        h2 { color: #007bff; margin-bottom: 15px; }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #e9ecef;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Final Test - Bible Picture Story Display</h1>
        
        <div class="test-section">
            <h2>🌐 Environment Check</h2>
            <div id="environmentResults"></div>
            <button onclick="runEnvironmentTests()">Run Environment Tests</button>
        </div>

        <div class="test-section">
            <h2>📁 File Upload Test</h2>
            <div class="upload-test" id="uploadTest">
                <p><strong>📤 Drag & Drop Test Area</strong></p>
                <p>Drag an image file here or click to select</p>
                <input type="file" id="fileInput" accept="image/*" multiple style="display: none;">
            </div>
            <div id="uploadResults"></div>
        </div>

        <div class="test-section">
            <h2>🔗 Application Integration Test</h2>
            <div id="integrationResults"></div>
            <button onclick="testApplicationIntegration()">Test Application Integration</button>
        </div>

        <div class="action-buttons">
            <h2>🚀 Launch Application</h2>
            <button onclick="openControlPanel()" style="background-color: #28a745;">📋 Open Control Panel</button>
            <button onclick="openProjector()" style="background-color: #17a2b8;">📽️ Open Projector</button>
            <button onclick="openGettingStarted()" style="background-color: #6f42c1;">📖 Getting Started</button>
        </div>

        <div class="test-section">
            <h2>📊 Test Summary</h2>
            <div id="testSummary">
                <p>Run the tests above to see the summary</p>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            environment: 0,
            upload: 0,
            integration: 0,
            total: 0
        };

        function addResult(containerId, message, status, details = '') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${status}`;
            
            const statusClass = status === 'pass' ? 'status-pass' : status === 'fail' ? 'status-fail' : 'status-pending';
            const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⏳';
            
            div.innerHTML = `
                <span class="status-indicator ${statusClass}"></span>
                <span><strong>${icon} ${message}</strong></span>
                ${details ? `<span style="margin-left: auto; font-size: 0.9em;">${details}</span>` : ''}
            `;
            container.appendChild(div);
            
            if (status === 'pass') testResults.total++;
            updateSummary();
        }

        function runEnvironmentTests() {
            const container = document.getElementById('environmentResults');
            container.innerHTML = '';
            testResults.environment = 0;
            
            // Test 1: Protocol
            const protocol = window.location.protocol;
            const protocolOK = protocol === 'http:' || protocol === 'https:';
            addResult('environmentResults', 'Server Protocol', protocolOK ? 'pass' : 'fail', protocol);
            if (protocolOK) testResults.environment++;
            
            // Test 2: File API
            const hasFileAPI = !!(window.File && window.FileReader && window.FileList && window.Blob);
            addResult('environmentResults', 'File API Support', hasFileAPI ? 'pass' : 'fail');
            if (hasFileAPI) testResults.environment++;
            
            // Test 3: Local Storage
            let hasLocalStorage = false;
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                hasLocalStorage = true;
            } catch (e) {}
            addResult('environmentResults', 'Local Storage', hasLocalStorage ? 'pass' : 'fail');
            if (hasLocalStorage) testResults.environment++;
            
            // Test 4: PostMessage
            const hasPostMessage = !!window.postMessage;
            addResult('environmentResults', 'PostMessage API', hasPostMessage ? 'pass' : 'fail');
            if (hasPostMessage) testResults.environment++;
            
            // Test 5: ES6
            let hasES6 = false;
            try {
                eval('const test = () => `ES6`;');
                hasES6 = true;
            } catch (e) {}
            addResult('environmentResults', 'ES6 Support', hasES6 ? 'pass' : 'fail');
            if (hasES6) testResults.environment++;
            
            console.log('Environment tests completed');
        }

        function testApplicationIntegration() {
            const container = document.getElementById('integrationResults');
            container.innerHTML = '';
            testResults.integration = 0;
            
            // Test 1: Control Panel Access
            fetch('index.html')
                .then(response => {
                    const accessible = response.ok;
                    addResult('integrationResults', 'Control Panel Access', accessible ? 'pass' : 'fail', response.status);
                    if (accessible) testResults.integration++;
                })
                .catch(error => {
                    addResult('integrationResults', 'Control Panel Access', 'fail', error.message);
                });
            
            // Test 2: JavaScript Files
            fetch('js/control-panel.js')
                .then(response => {
                    const accessible = response.ok;
                    addResult('integrationResults', 'JavaScript Files', accessible ? 'pass' : 'fail', response.status);
                    if (accessible) testResults.integration++;
                })
                .catch(error => {
                    addResult('integrationResults', 'JavaScript Files', 'fail', error.message);
                });
            
            // Test 3: CSS Files
            fetch('css/control-panel.css')
                .then(response => {
                    const accessible = response.ok;
                    addResult('integrationResults', 'CSS Files', accessible ? 'pass' : 'fail', response.status);
                    if (accessible) testResults.integration++;
                })
                .catch(error => {
                    addResult('integrationResults', 'CSS Files', 'fail', error.message);
                });
            
            // Test 4: Sample Story
            fetch('sample-story.json')
                .then(response => {
                    const accessible = response.ok;
                    addResult('integrationResults', 'Sample Story File', accessible ? 'pass' : 'fail', response.status);
                    if (accessible) testResults.integration++;
                })
                .catch(error => {
                    addResult('integrationResults', 'Sample Story File', 'fail', error.message);
                });
            
            // Test 5: Window Opening
            try {
                const testWindow = window.open('about:blank', 'test', 'width=300,height=200');
                if (testWindow) {
                    addResult('integrationResults', 'Window Opening', 'pass', 'Pop-ups allowed');
                    testResults.integration++;
                    setTimeout(() => testWindow.close(), 1000);
                } else {
                    addResult('integrationResults', 'Window Opening', 'fail', 'Pop-ups blocked');
                }
            } catch (error) {
                addResult('integrationResults', 'Window Opening', 'fail', error.message);
            }
        }

        function updateSummary() {
            const summary = document.getElementById('testSummary');
            const total = testResults.environment + testResults.upload + testResults.integration;
            const maxTotal = 12; // Maximum possible score
            
            let status = 'info';
            let message = 'Tests in progress...';
            
            if (total >= 10) {
                status = 'pass';
                message = '🎉 Excellent! All systems ready for Bible Picture Story Display!';
            } else if (total >= 7) {
                status = 'info';
                message = '⚠️ Good! Most features should work, but check failed tests.';
            } else if (total > 0) {
                status = 'fail';
                message = '❌ Issues detected. Please resolve failed tests before using the application.';
            }
            
            summary.innerHTML = `
                <div class="test-result ${status}">
                    <strong>Overall Score: ${total}/${maxTotal}</strong>
                    <p>${message}</p>
                </div>
                <div style="margin-top: 15px;">
                    <strong>Breakdown:</strong>
                    <ul>
                        <li>Environment: ${testResults.environment}/5</li>
                        <li>File Upload: ${testResults.upload}/2</li>
                        <li>Integration: ${testResults.integration}/5</li>
                    </ul>
                </div>
            `;
        }

        function openControlPanel() {
            window.open('index.html', '_blank');
        }

        function openProjector() {
            window.open('projector.html', '_blank');
        }

        function openGettingStarted() {
            window.open('getting-started.html', '_blank');
        }

        // File upload test
        const uploadTest = document.getElementById('uploadTest');
        const fileInput = document.getElementById('fileInput');

        uploadTest.addEventListener('click', () => fileInput.click());

        fileInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            testFileUpload(files);
        });

        uploadTest.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadTest.classList.add('dragover');
        });

        uploadTest.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadTest.classList.remove('dragover');
        });

        uploadTest.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadTest.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            testFileUpload(files);
        });

        function testFileUpload(files) {
            const container = document.getElementById('uploadResults');
            container.innerHTML = '';
            testResults.upload = 0;
            
            if (files.length === 0) {
                addResult('uploadResults', 'No files selected', 'info');
                return;
            }
            
            addResult('uploadResults', 'File Selection', 'pass', `${files.length} file(s)`);
            testResults.upload++;
            
            const imageFiles = files.filter(file => file.type.startsWith('image/'));
            if (imageFiles.length > 0) {
                addResult('uploadResults', 'Image File Detection', 'pass', `${imageFiles.length} image(s)`);
                testResults.upload++;
                
                // Test reading the first image
                const reader = new FileReader();
                reader.onload = function() {
                    console.log('File read successfully');
                };
                reader.onerror = function() {
                    console.error('File read failed');
                };
                reader.readAsDataURL(imageFiles[0]);
            } else {
                addResult('uploadResults', 'Image File Detection', 'fail', 'No image files found');
            }
        }

        // Auto-run environment tests on load
        window.addEventListener('load', function() {
            console.log('Final test page loaded');
            setTimeout(runEnvironmentTests, 1000);
        });
    </script>
</body>
</html>
